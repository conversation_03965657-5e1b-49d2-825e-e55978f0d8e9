import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { useFormContext } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";

export function ConsultationOwner() {
  const form = useFormContext<FormSchemaType>();

  return (
    <>
      <FormField
        control={form.control}
        name="consultationOwner"
        render={({ field }) => (
          <FormItem>
            <FormLabel aria-required required>Who is this consultation for?</FormLabel>
            <Select
              value={field.value as string}
              defaultValue={""}
              onValueChange={(value) => {
                field.onChange(value);
                (["hmoId", "principalHmoId"] satisfies Array<keyof FormSchemaType>).forEach(field => form.setValue(field, ""));
              }}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select consultation owner" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="Myself">Myself</SelectItem>
                <SelectItem value="Dependant">Dependant</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}