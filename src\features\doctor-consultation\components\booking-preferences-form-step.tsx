import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  BookingPreferencesStepFormSchema,
  useDoctorConsultationFormDataStore,
} from "@/features/doctor-consultation/hooks/useDoctorConsultationFormData";
import { useDoctorConsultationFormSteps } from "@/features/doctor-consultation/hooks/useDoctorConsultationFormSteps";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, startOfToday } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { restApi } from "@/lib/rest-api-client";
import { AxiosError } from "axios";
import { ConsultationTimeField } from "@/components/consultation-time-field";
import { useCreateConsultation } from "@/features/consultation/hooks/use-create-consultation";
import { ConsultationDoctor } from "@/lib/factories";
import { useToast } from "@/hooks/use-toast";

type BookingPreferencesStepProps = {
  doctorProfile: ConsultationDoctor;
};

export function BookingPreferencesStep(props: BookingPreferencesStepProps) {
  const { doctorProfile } = props;
  const formDataStore = useDoctorConsultationFormDataStore();
  const { setCurrentStep } = useDoctorConsultationFormSteps();

  const form = useForm<z.infer<typeof BookingPreferencesStepFormSchema>>({
    resolver: zodResolver(BookingPreferencesStepFormSchema),
    defaultValues: {
      date: formDataStore.date ? new Date(formDataStore.date) : undefined,
      time: undefined,
      channel: formDataStore.channel,
      email: formDataStore.email,
    },
  });
  const selectedDate = form.watch("date");

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const { createConsultation } = useCreateConsultation();

  const { toast } = useToast();

  const onSubmit = async (
    values: z.infer<typeof BookingPreferencesStepFormSchema>,
  ) => {
    formDataStore.setFormData(values);

    const { userInfo, profileNotExist } = await getUserProfileByEmail(
      values.email,
    );

    if (profileNotExist) {
      setCurrentStep("personal-information");
    } else if (userInfo) {
      // validate formDataStore
      if (
        !(
          formDataStore.symptoms &&
          formDataStore.firstNotice &&
          formDataStore.discomfortLevel
        )
      ) {
        toast({
          title: "Please fill out all the fields",
          description:
            "Go back to previous step and ensure all fields are filled",
          variant: "destructive",
        });
        return;
      }

      createConsultation({
        // user data
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        gender: userInfo.gender,
        phoneNumber: userInfo.phoneNumber,

        // previous step data
        symptoms: formDataStore.symptoms,
        firstNotice: formDataStore.firstNotice,
        discomfortLevel: formDataStore.discomfortLevel,
        description: formDataStore.description,

        // current step data
        date: values.date,
        time: values.time,
        channel: values.channel,
        email: values.email,

        doctorId: doctorProfile.id,
        amount: doctorProfile.fee,
        providerId: doctorProfile?.providerId,
        consultationType: "scheduled",
        createdThrough: "doctor-direct",
      });
    }
  };

  return (
    <div>
      <h2 className="text-xl font-medium">Booking Preference</h2>
      <p className="text-[13px] text-tertiary mt-0.5 mb-3">
        Pick a suitable time and communication channel for your consultation
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="mb-1">Select a date</FormLabel>
                <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(e) => {
                        field.onChange(e);
                        setIsCalendarOpen(false);
                      }}
                      disabled={(date) => date < startOfToday()}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="time"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Select a time</FormLabel>
                <ConsultationTimeField
                  selectedDate={selectedDate}
                  onChange={(value) => {
                    field.onChange(value);
                  }}
                  defaultValue={field.value}
                  doctorProfileId={doctorProfile.id}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="channel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Communication Channel</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="chat">Chat</SelectItem>
                    <SelectItem value="voice">Voice</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input type="email" {...field} placeholder="Email address" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-8 lg:pt-6">
            <Button
              className="w-full"
              type="submit"
              loading={form.formState.isSubmitting}
            >
              Next
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

type UserProfile = {
  firstName: string;
  lastName: string;
  gender: "male" | "female";
  phoneNumber: string;
};

async function getUserProfileByEmail(email: string) {
  try {
    const { data } = await restApi.post("/consultations/validate/email", {
      email,
    });

    return {
      profileNotExist: false as const,
      userInfo: data.data?.data as UserProfile,
    };
  } catch (error) {
    // separate 'no profile' error from other errors so that other kinds of errors(e.g. server downtime or network) do not trigger wrong behavior (i.e. navigating to registration step)
    if (isNoProfileError(error)) {
      return {
        profileNotExist: true as const,
      };
    } else {
      throw error;
    }
  }
}

function isNoProfileError(error: unknown) {
  return (
    error instanceof AxiosError && error.response?.data?.statusCode === 404
  );
}
