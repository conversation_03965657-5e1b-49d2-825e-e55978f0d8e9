import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function AdditionalInformation() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="description"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Additional Information (Optional)</FormLabel>
          <FormControl>
            <Textarea className="resize-none" placeholder="Enter additional information" {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}