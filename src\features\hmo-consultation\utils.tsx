import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import {
  Consultation,
  Gender,
  GetHMOUserResponse,
} from "@/features/hmo-consultation/types";
import {
  GetPharmaciesFromWellaHealthQuery,
  Exact,
  InputMaybe,
  Scalars,
} from "@/graphql/generated/graphql";
import { defaultProfilePics } from "@/lib/constants";
import { restApi } from "@/lib/rest-api-client";
import { isObject, isValidDate, trimObjectStrings } from "@/lib/utils";
import { LazyQueryExecFunction } from "@apollo/client";
import { format } from "date-fns";
import { UseFormSetValue } from "react-hook-form";

export const steps = Object.freeze({
  INTRO_FORM: "INTRO_FORM",
  SELECT_DOCTOR: "SELECT_DOCTOR",
  CREATE: "CREATE",
});

export const hospitalSteps = Object.freeze({
  INTRO_FORM: "INTRO_FORM",
  SELECT_HOSPITAL: "SELECT_HOSPITAL",
  SELECT_HOSPITAL_DOCTOR: "SELECT_HOSPITAL_DOCTOR",
  SELECT_DOCTOR: "SELECT_DOCTOR",
  CREATE: "CREATE",
});

export const getNextStepFromIntroForm = (
  isFollowUp: boolean,
  isHospitalConsultation: boolean,
) => {
  return isFollowUp
    ? steps.SELECT_DOCTOR
    : isHospitalConsultation
      ? hospitalSteps.SELECT_HOSPITAL
      : steps.CREATE;
};

export const getPrevStepInCreateConsultationForm = (
  isFollowUp: boolean,
  isHospitalConsultation: boolean,
) => {
  if (isFollowUp) return hospitalSteps.SELECT_DOCTOR;
  if (isHospitalConsultation) return hospitalSteps.SELECT_HOSPITAL_DOCTOR;
  return steps.INTRO_FORM;
};

type ValidateFollowFieldsReturnType =
  | {
      isValid: false;
      error: {
        name: keyof FormSchemaType;
        message: string;
      };
    }
  | {
      isValid: true;
      error: null;
    };

export const validateFollowFields = (
  values: FormSchemaType,
): ValidateFollowFieldsReturnType => {
  const { isFollowUp, followUpConsultationId, doctor } = values;
  if (!isFollowUp) {
    return {
      isValid: false as const,
      error: {
        name: "isFollowUp",
        message: "Error with isFollowUp value",
      },
    };
  }

  if (!followUpConsultationId) {
    return {
      isValid: false as const,
      error: {
        name: "followUpConsultationId",
        message: "No Follow up consultation ID found!",
      },
    };
  }

  if (!doctor) {
    return {
      isValid: false as const,
      error: {
        name: "doctor",
        message: "No Follow up Doctor ID found!",
      },
    };
  }

  return {
    isValid: true as const,
    error: null,
  };
};

type PharmacyValidateArgs = {
  hasPharmacyFeature: boolean;
  values: FormSchemaType;
  pharmacies: GetPharmaciesFromWellaHealthQuery["getPharmaciesFromWellaHealth"]["data"];
  isFollowUp?: boolean;
};

type PharmacyValidateReturnType =
  | {
      isValid: true;
      error: null;
    }
  | {
      isValid: false;
      error: {
        name: keyof FormSchemaType | "none";
        message: string;
      };
    };

// This seems to be validating the pharmacy delivery values...
export const extraValidate = ({
  hasPharmacyFeature,
  values,
  pharmacies = [],
  isFollowUp = false,
}: PharmacyValidateArgs): PharmacyValidateReturnType => {
  try {
    if (isFollowUp) {
      const result = validateFollowFields(values);
      if (!result.isValid) return result;
    }

    if (!hasPharmacyFeature) return { isValid: true as const, error: null };
    if (pharmacies?.length < 1) return { isValid: true as const, error: null };

    if (
      !values?.pharmacyCode ||
      values?.pharmacyCode === "" ||
      values?.pharmacyCode === "Select a pharmacy"
    ) {
      return {
        isValid: false as const,
        error: { name: "pharmacyCode", message: "Please select a pharmacy." },
      };
    }

    if (typeof values?.isDelivery !== "boolean") {
      return {
        isValid: false as const,
        error: {
          name: "isDelivery",
          message: "Please select a delivery option.",
        },
      };
    }

    return { isValid: true as const, error: null };
  } catch (error) {
    console.error("error from validateValues func:", error);
    return {
      isValid: false,
      error: {
        name: "none",
        message: "Something went wrong while trying to validate this form.",
      },
    };
  }
};

export const addValuesToForm = (
  setValue: UseFormSetValue<FormSchemaType>,
  obj: Partial<FormSchemaType>,
) => {
  try {
    const keysArr = Object.keys(obj) as (keyof FormSchemaType)[];
    for (let index = 0; index < keysArr.length; index++) {
      const key = keysArr[index];
      if (setValue) setValue(key, obj[key]);
    }
  } catch (error) {
    console.error("Couldn't update form values", error);
  }
};

export const addHMOUserDataToFormState = (
  setValue: UseFormSetValue<FormSchemaType>,
  hmoUserData: GetHMOUserResponse["data"][0],
) => {
  try {
    const data = trimObjectStrings(hmoUserData) || {};

    const {
      providerId,
      firstName,
      lastName,
      email,
      phone,
      photo,
      dob,
      gender,
    } = data;
    const phoneNumber =
      typeof phone === "string" ? phone?.replace(/\s+/g, "") : phone;

    const companyIdObj = data?.companyId;
    const enrolleeCompanyId = isObject(companyIdObj)
      ? companyIdObj?._id
      : typeof companyIdObj === "string"
        ? companyIdObj
        : null;
    const companyId = enrolleeCompanyId ? { companyId: enrolleeCompanyId } : {};

    const obj = {
      providerId,
      firstName,
      lastName,
      email,
      phoneNumber,
      dob,
      gender,
      image: photo ? photo : defaultProfilePics,
      ...companyId,
    };

    addValuesToForm(setValue, obj);
  } catch (error) {
    console.error(
      "Couldn't update form values from addHMOUserDataToFormState func:",
      error,
    );
  }
};

export const updateFollowUpForm = (
  setValue: UseFormSetValue<FormSchemaType>,
  prevConsultation: Consultation,
) => {
  try {
    const {
      doctorData,
      symptoms,
      _id,
      patientData,
      firstNotice,
      discomfortLevel,
    } = prevConsultation || {};
    const gender = patientData?.gender?.toLowerCase() as Gender;
    const dateOfBirth = isValidDate(new Date(patientData?.dob || ""))
      ? patientData?.dob
      : undefined;

    const formattedDob = dateOfBirth
      ? format(dateOfBirth || "", "yyyy-MM-dd")
      : "";

    const obj = {
      isFollowUp: true,
      followUpConsultationId: _id,
      doctor: doctorData?._id,
      symptoms,
      gender,
      firstNotice,
      discomfortLevel,
      dob: formattedDob,
    };
    addValuesToForm(setValue, obj);
  } catch (error) {
    console.error(
      "Couldn't update form values from updateFormValues func:",
      error,
    );
  }
};

export const formatValues = (
  values: FormSchemaType,
  withPharmacy: boolean,
  isHospitalConsultation: boolean,
) => {
  const isFollowUp = values?.isFollowUp;
  const isDependant = values?.principalHmoId;

  const pharmacyValues = withPharmacy
    ? {
        pharmacyCode: values?.pharmacyCode,
        pharmacyAddress: values?.pharmacyAddress,
        pharmacyName: values?.pharmacyName,
        isDelivery: values?.isDelivery,
      }
    : {};

  const followUpValues = isFollowUp
    ? {
        isFollowUp: values?.isFollowUp,
        followUpConsultationId: values?.followUpConsultationId,
        doctor: values?.doctor,
      }
    : {};

  const dependantValues = isDependant
    ? { principalHmoId: values?.principalHmoId }
    : {};

  const hospitalConsultationValues = isHospitalConsultation
    ? {
        // @ts-expect-error need to type form schema to include 'time' field
        time: values?.time,
        doctor: values?.doctor,
      }
    : {};

  const type = isHospitalConsultation ? "scheduled" : "instant";
  const hospitalProviderId = useHMOStore.getState().selectedHospital?._id;
  const providerId = isHospitalConsultation
    ? hospitalProviderId
    : values?.providerId;

  const companyId = values?.companyId ? { companyId: values?.companyId } : {};

  return {
    consultationOwner: values?.consultationOwner,
    symptoms: values?.symptoms,
    discomfortLevel: values?.discomfortLevel,
    description: values?.description,
    firstNotice: values?.firstNotice,
    type,
    providerId,
    contactMedium: values?.contactMedium,
    patientData: {
      email: values?.email,
      firstName: values?.firstName,
      lastName: values?.lastName,
      phoneNumber: values?.phoneNumber,
      gender: values?.gender,
      hmoId: values?.hmoId,
      image: values?.image,
      dob: values?.dob,
      ...companyId,
    },
    timeOffset: "",
    createdThrough: "weblink",
    address: values?.address,
    ...pharmacyValues,
    ...followUpValues,
    ...dependantValues,
    ...hospitalConsultationValues,
  };
};

type GetPharmaciesFn = LazyQueryExecFunction<
  GetPharmaciesFromWellaHealthQuery,
  Exact<{
    lga?: InputMaybe<Scalars["String"]["input"]>;
    state: Scalars["String"]["input"];
  }>
>;

export const getClosestPharmacies = async (
  getPharmacies: GetPharmaciesFn,
  address: Record<string, string>,
) => {
  try {
    const stateAndLgaRes = await getPharmacies({
      variables: { lga: address?.lga, state: address?.state },
    });
    const pharmacyArr =
      stateAndLgaRes?.data?.getPharmaciesFromWellaHealth?.data || [];

    if (pharmacyArr && pharmacyArr?.length > 0) {
      return pharmacyArr;
    }

    const stateRes = await getPharmacies({
      variables: { state: address?.state },
    });
    return stateRes?.data?.getPharmaciesFromWellaHealth?.data || [];
  } catch (error) {
    console.error("Error from getClosestPharmacies func:", error);
  }
};

export const hideInputForFollowUp = (
  isFollowUp: boolean,
  fieldHasError: boolean,
) => {
  return isFollowUp && !fieldHasError;
};

export const updateUserInfo = async (
  values: FormSchemaType,
  userObj: GetHMOUserResponse["data"][0] | undefined,
) => {
  const UPDATE_USER_INFO = (
    userData: Record<string, string>,
    userId: string,
  ) => {
    return restApi.patch(`enrollees/${userId}`, userData);
  };

  let updateObject: Record<string, string> = {};

  try {
    const hmoId = values?.hmoId;
    const providerId = userObj?.providerId;

    // UPDATE EMAIL
    if (values.email && values?.email !== userObj?.email) {
      updateObject = { ...updateObject, email: values?.email };
    }

    // UPDATE PHONE NUMBER
    if (values?.phoneNumber && values?.phoneNumber !== userObj?.phone) {
      updateObject = { ...updateObject, phone: values?.phoneNumber };
    }

    // UPDATE DOB
    if (values?.dob && values?.dob !== userObj?.dob) {
      updateObject = { ...updateObject, dob: values?.dob };
    }

    // UPDATE GENDER
    if (values?.gender && values?.gender !== userObj?.gender) {
      updateObject = { ...updateObject, gender: values?.gender };
    }

    if (Object.keys(updateObject).length > 0) {
      await UPDATE_USER_INFO(
        {
          ...updateObject,
          hmoId,
          providerId: providerId || "",
        },
        userObj?._id || "",
      );
    }
  } catch (error) {
    console.error(error);
  }
};

export const colorToHex = (color: string): string | null => {
  // Check if it's a valid hex code
  const hexRegex = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/;
  if (hexRegex.test(color)) return color; // Return if already a hex

  // Convert named color to hex using Canvas
  const ctx = document.createElement("canvas").getContext("2d");
  if (!ctx) return null;

  ctx.fillStyle = color;
  const computedColor = ctx.fillStyle;

  // Check if the browser recognized the color
  return hexRegex.test(computedColor) ? computedColor : null;
};
