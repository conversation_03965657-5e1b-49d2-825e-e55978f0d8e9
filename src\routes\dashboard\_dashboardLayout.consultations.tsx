import { useState } from "react";
import { formatName, isValueValidDate } from "@/lib/utils";
import { useQuery } from "@apollo/client";
import { gql } from "@/graphql/generated";
import { format } from "date-fns";
// import { MoreHorizontal } from "lucide-react";
// import { Button } from "@/components/ui/button";
import { StatusPill } from "@/components/StatusPill";
import { PageInfoType } from "@/lib/typescript/types";
import { CustomTable } from "@/components/CustomTable";
import { createFileRoute } from "@tanstack/react-router";
import { TableCell, TableRow } from "@/components/ui/table";
import { consultationTableHeader } from "@/lib/table-headers";
import { TableRowSkeleton } from "@/components/TableRowSkeleton";
import { selectDashboardData, useDashboardStore } from "@/store/dashboardStore";
import { defaultPageInfo } from "@/lib/mock-data";
import { NoData } from "@/components/EmptyStates";

export const Route = createFileRoute(
  "/dashboard/_dashboardLayout/consultations",
)({
  component: Consultations,
});

const GET_ALL_CONSULTATIONS = gql(`
  query getAllUserConsultations ($first: Int, $orderBy: String, $page: Int, $patientId: String) {
    getConsultations(
      filterBy: { patient: $patientId }
      first: $first
      orderBy: $orderBy
      page: $page
    ) {
      data {
        _id
        status
        type
        time
        createdAt
        contactMedium
        doctor{
          _id
          firstName
          lastName
        }
      }
      pageInfo {
        totalDocs
        limit
        offset
        hasPrevPage
        hasNextPage
        page
        totalPages
        pagingCounter
        prevPage
        nextPage
      }
    }
  }
`);

function Consultations() {
  const userProfile = useDashboardStore(selectDashboardData)?.profile;
  const patientId = userProfile?._id;

  const [pageInfo, setPageInfo] = useState<PageInfoType>(defaultPageInfo);

  const { loading, data, error } = useQuery(GET_ALL_CONSULTATIONS, {
    variables: {
      first: pageInfo?.limit,
      page: pageInfo?.page,
      orderBy: "-createdAt",
      patientId,
    },
    onCompleted(data) {
      setPageInfo(data.getConsultations.pageInfo);
    },
  });

  const consultations = data?.getConsultations?.data || [];

  return (
    <main>
      <div>
        <div className="my-10">
          <CustomTable
            headers={consultationTableHeader}
            hasPagination={true}
            paginationInfo={pageInfo}
            onPageChange={(page: number) =>
              setPageInfo((prev) => ({ ...prev, page }))
            }
          >
            {loading ? (
              Array(10)
                .fill(10)
                .map((_, idx) => {
                  return <TableRowSkeleton cellCount={4} key={idx} />;
                })
            ) : error ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData
                    isError={true}
                    text="An Error occurred"
                    info={error?.message}
                  />
                </TableCell>
              </TableRow>
            ) : consultations?.length < 1 ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData isError={false} text="No consultations" />
                </TableCell>
              </TableRow>
            ) : (
              consultations.map((consultation, idx) => {
                const timeData = consultation?.createdAt;
                const dateTime = isValueValidDate(timeData)
                  ? format(new Date(timeData), "dd/MM/yyyy - HH:mm")
                  : "No date";

                const doctorName = formatName(
                  consultation?.doctor?.firstName,
                  consultation?.doctor?.lastName,
                  "Dr.",
                );
                const status = consultation?.status;

                return (
                  <TableRow key={idx} className="">
                    <TableCell>{dateTime}</TableCell>
                    <TableCell>{doctorName}</TableCell>
                    <TableCell>{consultation?.contactMedium}</TableCell>
                    <TableCell>
                      <StatusPill
                        label={status ? status : "No status"}
                        type={
                          status === "cancelled"
                            ? "error"
                            : status === "completed"
                              ? "success"
                              : status === "ongoing"
                                ? "normal"
                                : "normal"
                        }
                      />
                    </TableCell>
                    {/* <TableCell className="text-right">
                    <MoreHorizontal className="h-5 w-5" />
                  </TableCell> */}
                  </TableRow>
                );
              })
            )}
          </CustomTable>
        </div>
      </div>
    </main>
  );
}
