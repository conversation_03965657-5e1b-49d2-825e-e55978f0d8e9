import { Button } from "@/components/ui/button";

type ErrorViewProps = {
  isNotFoundError: boolean;
};

export function ErrorView(props: ErrorViewProps) {
  const { isNotFoundError } = props;

  return (
    <div className="rounded-lg bg-white border border-neutral-100 h-full lg:h-[492px] ">
      <div className="text-center px-4 w-full h-full flex flex-col justify-center items-center">
        {isNotFoundError ? (
          <>
            <p className="text-lg font-medium">Doctor not found!</p>
            <p className="text-tertiary mt-2">
              We couldn't find a doctor for this link. Please check the
              link/address.
            </p>
          </>
        ) : (
          <>
            <p className="text-lg font-medium">Something went wrong!</p>
            <p className="text-tertiary mt-2">Please check the link/address.</p>
            <p className="text-tertiary mt-2 text-xs">or</p>
            <Button
              className="mt-4 w-[200px]"
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </>
        )}
      </div>
    </div>
  );
}
