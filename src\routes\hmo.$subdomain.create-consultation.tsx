import { IntroForm } from "@/features/hmo-consultation/components/intro-form";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { hospitalSteps, steps } from "@/features/hmo-consultation/utils";
import { createFileRoute, useRouterState } from "@tanstack/react-router";
import { PropsWithChildren, useEffect } from "react";

import { FormProvider, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { generateFullSchema } from "@/features/hmo-consultation/schema";
import { NewConsultation } from "@/features/hmo-consultation/components/new-consultation";
import { SelectDoctor } from "@/features/hmo-consultation/components/select-doctor-step";
import { SelectHospital } from "@/features/hmo-consultation/components/select-hospital";
import { SelectHospitalDoctor } from "@/features/hmo-consultation/components/select-hospital-doctor";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { convertExternalViaToCreatedThrough } from "@/lib/utils";

export const Route = createFileRoute("/hmo/$subdomain/create-consultation")({
  component: RouteComponent,
});

// type FormStep = (typeof steps)[keyof typeof steps]

const hospitalForms = {
  [hospitalSteps.INTRO_FORM]: <IntroForm />,
  [hospitalSteps.SELECT_HOSPITAL]: <SelectHospital />,
  [hospitalSteps.SELECT_HOSPITAL_DOCTOR]: <SelectHospitalDoctor />,
  [hospitalSteps.SELECT_DOCTOR]: <SelectDoctor />,
  [hospitalSteps.CREATE]: <NewConsultation />,
};

const instantConsultForms = {
  [steps.INTRO_FORM]: <IntroForm />,
  [steps.SELECT_DOCTOR]: <SelectDoctor />,
  [steps.CREATE]: <NewConsultation />,
};

const getForms = (isHospitalConsultation: boolean) =>
  isHospitalConsultation ? hospitalForms : instantConsultForms;

function RouteComponent() {
  const formStep = useHMOStore((s) => s.formStep);
  const providerFeatures = useHMOStore((s) => s.providerFeatures);
  const isHospitalConsultation = useHMOStore((s) => !!s.isHospitalConsultation);

  const hasPharmacyFeature = !!providerFeatures?.hasPharmacyFeature;
  const hasFollowUpFeature = !!providerFeatures?.hasFollowUpFeature;
  const hasAddressFeature = !!providerFeatures?.hasAddressFeature;

  const routerState = useRouterState();
  const via =
    // @ts-expect-error will type more strictly later
    convertExternalViaToCreatedThrough(routerState?.location?.state?.via);

  const savedFormData = sessionStorage.getItem("RHF_Data") || "{}";
  const parsedFormData = JSON.parse(savedFormData) || {};
  const schema = generateFullSchema(hasPharmacyFeature)(hasFollowUpFeature)(
    isHospitalConsultation,
  )(hasAddressFeature);
  const schemaKeys = Object.keys(schema.fields);
  const filteredDefaultValues = Object.keys(parsedFormData)
    .filter((key) => schemaKeys.includes(key))
    .reduce((obj, key) => {
      // @ts-expect-error to type
      obj[key] = parsedFormData[key];
      return obj;
    }, {});
  const type = isHospitalConsultation ? "scheduled" : "instant";

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      ...filteredDefaultValues,
      // @ts-expect-error type schema to include 'type' field
      type,
      createdThrough: via,
    },
    mode: "onBlur",
    shouldFocusError: true,
    reValidateMode: "onChange",
  });

  // Watch all form values to update sessionStorage on change
  const formValues = methods.watch();
  useEffect(() => {
    sessionStorage.setItem("RHF_Data", JSON.stringify(formValues));
  }, [formValues]);

  return (
    <FormProvider {...methods}>
      <FormsContainer>
        {
          /* @ts-expect-error need to type form steps */
          getForms(isHospitalConsultation)[formStep!]
        }
      </FormsContainer>
    </FormProvider>
  );
}

type FormsContainerProps = PropsWithChildren;

// const FORM_STORAGE_KEY = "HMO_Form_RHF_Data"

function FormsContainer(props: FormsContainerProps) {
  const { children } = props;
  const widgetColor = useWidgetColor();
  const widgetLogo = useHMOStore((s) => s.partnerInfo?.widgetLogo);

  return (
    <div className="flex justify-center items-center h-screen px-4">
      <div className="relative max-w-[482px] w-full overflow-hidden">
        <div
          style={{ backgroundColor: widgetColor }}
          className="absolute -z-10 top-0 bottom-0 left-0 right-0 rounded-[24px] bg-primary opacity-20"
        ></div>
        <div className="rounded-2xl overflow-hidden">
          <div className="max-h-[calc(100vh-80px)] flex flex-col m-2 bg-[#fbfbfb] rounded-2xl p-6 relative">
            {/* Logo */}
            <div className="py-4 text-center border-b border-b-[rgb(151,151,151)]">
              <img
                src={widgetLogo || ""}
                className="max-h-[64px] max-w-[160px] object-contain mx-auto"
                width={160}
                height={45}
              />
            </div>

            <div className="overflow-y-auto no-scrollbar">
              <h1 className="text-center text-[#2c2c2c] text-2xl mt-4">
                Consult a Doctor
              </h1>
              <p className="text-[#666] text-xs text-center mt-4 leading-relaxed">
                Fill the form below and we'll connect you to a doctor
              </p>

              <div className="px-0.5 m-1">{children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
