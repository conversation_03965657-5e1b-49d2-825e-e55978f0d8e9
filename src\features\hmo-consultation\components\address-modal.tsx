import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "@/components/ui/dialog";
import { FormLabel } from "@/components/ui/form";
// import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { statesAndLgas } from "@/lib/states-and-lgas";
import { checkAddress, defaultAddress } from "@/lib/utils";
import { useState } from "react";

type AddressModalProps = {
  open: boolean;
  onClose: () => void;
  addAddress: (address: {
    /* street: string; */ state: string;
    lga: string;
  }) => void;
};

export function AddressModal(props: AddressModalProps) {
  const { open, onClose, addAddress } = props;
  const [error, setError] = useState(false);
  const [address, setAddress] = useState({ ...defaultAddress });

  const handleUpdateAddress = (name: string, value: string) => {
    setError(false);

    if (!name || name === "") return;
    if (name === "state") {
      setAddress((prev) => {
        return {
          ...prev,
          lga: "",
          [name]: value,
        };
      });
    } else {
      setAddress((prev) => {
        return {
          ...prev,
          [name]: value,
        };
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={open ? onClose : undefined}>
      <DialogPortal>
        <DialogOverlay>
          <DialogContent className="!overflow-visible">
            <DialogHeader>
              <DialogTitle>Enter your address</DialogTitle>
              <DialogDescription>
                {" "}
                NB: This address will be used to deliver any
                prescriptions(drugs) from the doctor.
              </DialogDescription>
            </DialogHeader>

            <div className="relative space-y-4 !z-[60]">
              {/* <div>
                <FormLabel required>Street</FormLabel>
                <Input
                  name="street"
                  value={address.street}
                  onChange={(e) =>
                    handleUpdateAddress("street", e.target.value)
                  }
                  placeholder="879 Dorthea Ford, festac"
                  className="mt-1 "
                />
              </div> */}

              <div className="relative z-[60]">
                <FormLabel required>State</FormLabel>
                <Select
                  onValueChange={(value) => handleUpdateAddress("state", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a state" />
                  </SelectTrigger>

                  <SelectContent className="max-h-60 !z-[1000]">
                    {Object.keys(statesAndLgas).map((state) => {
                      return <SelectItem value={state}>{state}</SelectItem>;
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <FormLabel required>LGA</FormLabel>
                <Select
                  onValueChange={(value) => handleUpdateAddress("lga", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select LGA" />
                  </SelectTrigger>

                  <SelectContent className="max-h-60 !z-[1000]">
                    {(statesAndLgas[address.state] || []).map((lga) => {
                      return <SelectItem value={lga}>{lga}</SelectItem>;
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="mt-4">
                {error && (
                  <p className="text-sm text-red-500 font-medium">
                    Please fill all fields.
                  </p>
                )}
                <Button
                  onClick={() => {
                    const isAddressCorrect = checkAddress(address);
                    if (!isAddressCorrect) {
                      setError(true);
                      return;
                    }
                    addAddress(address);
                    onClose();
                  }}
                  className="w-full"
                >
                  Add Address
                </Button>
              </div>
            </div>
          </DialogContent>
        </DialogOverlay>
      </DialogPortal>
    </Dialog>
  );
}
