import { isObject } from "../utils";
import { ProfileDataType, ProfileType } from "../typescript/types";

export const profileFactory = (profile: ProfileDataType) => {
  const profileObject = isObject(profile) ? profile : ({} as ProfileDataType);
  return {
    _id: profileObject?._id || "",
    firstName: profileObject?.firstName || "",
    lastName: profileObject?.lastName || "",
    email: profileObject?.accountId?.email || "",
    height: profileObject?.height || 0,
    weight: profileObject?.weight || 0,
    bloodGroup: profileObject?.bloodGroup || "No Blood Group",
    genotype: profileObject?.genotype || null,
    gender: profileObject?.gender || "No Gender",
    phoneNumber: profileObject?.phoneNumber || null,
    providerId: profileObject?.providerId?._id || null,
    plan: profileObject?.plan || null,
    status: profileObject?.status || null,
    consultations: profileObject?.consultations || 0,
    image: profileObject?.image || null,
    rating: profileObject?.rating || null,
    accountId: profileObject?.accountId?._id || null,
  } as ProfileType;
};
