import { usePartnerData } from "@/features/consultation/hooks/use-partner-data";
import { colorToHex } from "@/features/hmo-consultation/utils";
import { healaColor } from "@/lib/mock-data";
import { useParams } from "@tanstack/react-router";

export const useWidgetColor = () => {
  const params = useParams({ strict: false });
  const subdomain = params?.subdomain as string;
  const { partnerData } = usePartnerData({ subdomain });
  const widgetColor = partnerData?.widgetColor || healaColor;
  const normalizedWidgetColor = colorToHex(widgetColor) || healaColor;

  return normalizedWidgetColor;
};
