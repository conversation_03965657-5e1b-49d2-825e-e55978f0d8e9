import React from "react";
import classNames from "classnames";
import { DoctorStarRating } from "@/components/doctor-star-rating";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronRight } from "lucide-react";
import { Doctor } from "@/features/hmo-consultation/types";
import { useWidgetColor } from "@/hooks/useWidgetColor";

type DoctorCardProps = {
  active: boolean;
  setActive: () => void;
  doctor: Doctor;
};

export const DoctorCard = (props: DoctorCardProps) => {
  const color = useWidgetColor();
  const { active = false, setActive, doctor } = props;
  const name = `Dr. ${doctor.firstName} ${doctor.lastName}`;

  return (
    <div
      style={active ? { borderColor: color } : {}}
      className={classNames(
        `flex items-center bg-white hover:bg-slate-50 justify-between border rounded-md !px-5 !py-7 cursor-pointer !mx-1 transition ease-in-out duration-700`,
        { "border-2": active },
      )}
      onClick={() => setActive()}
    >
      <div className="flex items-center space-x-5">
        <Avatar>
          <AvatarImage src={doctor.picture} />
          <AvatarFallback>
            {[doctor.firstName || "", doctor.lastName || ""]
              .map((name: string) => name.substring(0, 1))
              .join("")}
          </AvatarFallback>
        </Avatar>
        <div>
          <p className="!text-base">{name}</p>
          <p className="!text-xs">{doctor.specialization}</p>
          <DoctorStarRating rating={doctor.rating || 0} />
        </div>
      </div>
      <ChevronRight />
    </div>
  );
};
