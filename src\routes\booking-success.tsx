import { Head } from "@/components/head";
import CalendarIcon from "@/components/icons/calendar-icon";
import CircleCheckIcon from "@/components/icons/circle-check-icon";
import GlobeIcon from "@/components/icons/globe-icon";
import VideoIcon from "@/components/icons/video-icon";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>oader } from "@/components/heala-loader";
import { restApi } from "@/lib/rest-api-client";
import { useQuery } from "@tanstack/react-query";
import { useQuery as useApolloQuery } from "@apollo/client";
import { createFileRoute, Link } from "@tanstack/react-router";
import { zodValidator } from "@tanstack/zod-adapter";
import { format } from "date-fns";
import { useMemo } from "react";
import { z } from "zod";
import { checkChargeQuery } from "@/graphql/queries";
import { useToast } from "@/hooks/use-toast";

export const Route = createFileRoute("/booking-success")({
  component: RouteComponent,
  validateSearch: zodValidator(
    z.object({
      consultationId: z.string(),
      apiKey: z.string(),
      reference: z.string(),
    }),
  ),
});

function RouteComponent() {
  const { consultationId, apiKey, reference } = Route.useSearch();
  const { toast } = useToast();

  useApolloQuery(checkChargeQuery, {
    variables: {
      data: { reference },
    },
    onError(error) {
      console.log("Check charge error: ", error);
      toast({
        title: "Couldn't verify your payment. Please contact support!",
        description: "Please contact support!",
        variant: "destructive",
      });
    },
  });

  const { data, isLoading } = useQuery({
    queryFn: async () =>
      await restApi.get(`/consultations/${consultationId}`, {
        headers: {
          Authorization: `Api-Key ${apiKey}`,
        },
      }),
    queryKey: ["consultationId", consultationId],
  });

  const consultationInfo = data?.data?.data?.data;

  const consultationInfoItems = useMemo(() => {
    if (!consultationInfo) return [];
    else
      return [
        {
          text: format(
            new Date(consultationInfo?.time),
            "hh:mm a, EEEE, MMMM dd, yyyy",
          ),
          icon: CalendarIcon,
        },
        {
          text: "West Africa Time",
          icon: GlobeIcon,
        },
        {
          text: "Invitation will be sent after doctor's approval",
          icon: VideoIcon,
        },
      ];
  }, [consultationInfo]);

  const doctorNameWithTitle = consultationInfo?.doctor
    ? `Dr. ${consultationInfo?.doctor.firstName} ${consultationInfo?.doctor.lastName}`
    : "";

  // const { trigger: triggerConfetti, ConfettiComponent } = useConfetti();
  // useEffect(() => {
  //   if (consultationInfo) {
  //     triggerConfetti();
  //   }
  // }, [consultationInfo, triggerConfetti]);

  // todo: error handling

  return (
    <>
      <Head
        title={`Appointment Successful ${doctorNameWithTitle ? " - " + doctorNameWithTitle : ""}`}
      />
      {/* <ConfettiComponent /> */}
      <div className="min-h-screen flex justify-center items-center bg-primary-50 px-4 py-4">
        {isLoading ? (
          <HealaLoader />
        ) : (
          <div className="flex flex-col items-center bg-white px-4 py-8 rounded-md text-center">
            <CircleCheckIcon className="w-14 h-14 lg:w-20 lg:h-20" />

            <div className="w-[80%] mx-auto">
              <h1 className="text-2xl lg:text-3xl font-medium mt-4">
                Appointment Successful
              </h1>
              <p className="mt-2">
                Your appointment with{" "}
                <span className="font-medium">{doctorNameWithTitle}</span> has
                been booked successfully
              </p>

              <div className="flex justify-center mt-8">
                <div className="space-y-3">
                  {consultationInfoItems.map((i) => (
                    <div className="flex gap-2">
                      <i.icon className="w-6 h-6" />
                      <p className="text-[#5D626C] text-sm text-left">
                        {i.text}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* todo: A calendar invitation.... callout */}

              <div className="mt-10">
                <Button asChild>
                  <Link to="/access">View consultations</Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
