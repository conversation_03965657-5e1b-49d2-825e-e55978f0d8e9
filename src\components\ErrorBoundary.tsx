import React, { Component, ReactNode } from "react";
import { NoData } from "./EmptyStates";
import { Button } from "./ui/button";

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error Boundary Caught an Error:", error, errorInfo);
    // Optionally log the error to an external service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="w-screen h-screen flex flex-col justify-center items-center space-y-5">
          <NoData
            isError={true}
            text="Something unexpected has happened."
            info={this.state.error?.message}
          />
          <Button onClick={() => window?.location?.reload()}>Refresh</Button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
