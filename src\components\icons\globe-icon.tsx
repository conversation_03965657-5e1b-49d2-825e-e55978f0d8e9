import { SVGProps } from "react";
const GlobeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={19}
    height={18}
    viewBox="0 0 19 18"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_1640_68927)">
      <path
        d="M9.5 0C7.71997 0 5.97991 0.527841 4.49987 1.51677C3.01983 2.50571 1.86628 3.91131 1.18509 5.55585C0.5039 7.20038 0.32567 9.00998 0.672937 10.7558C1.0202 12.5016 1.87737 14.1053 3.13604 15.364C4.39472 16.6226 5.99836 17.4798 7.74419 17.8271C9.49002 18.1743 11.2996 17.9961 12.9442 17.3149C14.5887 16.6337 15.9943 15.4802 16.9832 14.0001C17.9722 12.5201 18.5 10.78 18.5 9C18.4974 6.61384 17.5484 4.32616 15.8611 2.63889C14.1738 0.951621 11.8862 0.00258081 9.5 0V0ZM17 9C17.0009 10.4698 16.5672 11.907 15.7535 13.131L14.8595 12.2363C14.7894 12.166 14.75 12.0708 14.75 11.9715V11.25C14.75 10.6533 14.513 10.081 14.091 9.65901C13.669 9.23705 13.0967 9 12.5 9H10.25C10.0511 9 9.86033 8.92098 9.71967 8.78033C9.57902 8.63968 9.5 8.44891 9.5 8.25V7.875C9.5 7.77554 9.53951 7.68016 9.60984 7.60984C9.68017 7.53951 9.77555 7.5 9.875 7.5C10.3723 7.5 10.8492 7.30246 11.2008 6.95083C11.5525 6.5992 11.75 6.12228 11.75 5.625V4.875C11.75 4.77554 11.7895 4.68016 11.8598 4.60984C11.9302 4.53951 12.0255 4.5 12.125 4.5H13.1593C13.656 4.49859 14.1322 4.30132 14.4845 3.951L14.7673 3.66825C15.4746 4.36431 16.0364 5.19423 16.4197 6.10963C16.8031 7.02503 17.0003 8.00758 17 9ZM2.03675 9.72225L4.40525 12.0908C4.61368 12.3005 4.86169 12.4668 5.13489 12.58C5.40809 12.6932 5.70104 12.7509 5.99675 12.75H8C8.19892 12.75 8.38968 12.829 8.53033 12.9697C8.67099 13.1103 8.75 13.3011 8.75 13.5V16.4618C7.02515 16.2857 5.4143 15.5184 4.1907 14.29C2.9671 13.0616 2.20609 11.4478 2.03675 9.72225ZM10.25 16.4618V13.5C10.25 12.9033 10.013 12.331 9.59099 11.909C9.16904 11.4871 8.59674 11.25 8 11.25H5.99675C5.89814 11.2501 5.80049 11.2307 5.70937 11.193C5.61826 11.1553 5.53547 11.1 5.46575 11.0303L2.12225 7.68675C2.33689 6.46665 2.85106 5.31892 3.61864 4.34654C4.38621 3.37415 5.38319 2.60753 6.5201 2.11546C7.65701 1.62339 8.89831 1.42126 10.1326 1.52721C11.3669 1.63316 12.5556 2.04387 13.592 2.7225L13.424 2.8905C13.3534 2.96012 13.2584 2.99941 13.1593 3H12.125C11.6277 3 11.1508 3.19754 10.7992 3.54918C10.4475 3.90081 10.25 4.37772 10.25 4.875V5.625C10.25 5.72446 10.2105 5.81984 10.1402 5.89017C10.0698 5.96049 9.97446 6 9.875 6C9.37772 6 8.90081 6.19754 8.54918 6.54918C8.19755 6.90081 8 7.37772 8 7.875V8.25C8 8.84674 8.23706 9.41903 8.65901 9.84099C9.08097 10.2629 9.65327 10.5 10.25 10.5H12.5C12.6989 10.5 12.8897 10.579 13.0303 10.7197C13.171 10.8603 13.25 11.0511 13.25 11.25V11.9715C13.2514 12.4683 13.4487 12.9445 13.799 13.2968L14.8018 14.2995C13.5803 15.5251 11.9717 16.2893 10.25 16.4618Z"
        fill="#5D626C"
      />
    </g>
    <defs>
      <clipPath id="clip0_1640_68927">
        <rect width={18} height={18} fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);
export default GlobeIcon;
