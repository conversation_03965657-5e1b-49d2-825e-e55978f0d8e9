import { <PERSON><PERSON><PERSON>oa<PERSON> } from "@/components/spinner-loader";
import { BackButton } from "@/features/hmo-consultation/components/back-button";
import { Doctor<PERSON><PERSON> } from "@/features/hmo-consultation/components/doctor-card";
import { NoDataView } from "@/features/hmo-consultation/components/no-data-view";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { Doctor } from "@/features/hmo-consultation/types";
import { hospitalSteps } from "@/features/hmo-consultation/utils";
import { restApi } from "@/lib/rest-api-client";
import { isValidDate } from "@/lib/utils";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { useQuery } from "@tanstack/react-query";
import { CalendarIcon } from "lucide-react";
import { useState } from "react";
import { useFormContext } from "react-hook-form";

export function SelectHospitalDoctor() {
  const setStoreValues = useHMOStore((s) => s.setValues);
  const setFormStep = useHMOStore((s) => s.setFormStep);
  const selectedHospital = useHMOStore((s) => s.selectedHospital);
  const hospitalId = selectedHospital?._id;
  const hospitalName = selectedHospital?.name;
  const selectedDocId = useHMOStore((s) => s.selectedDoctor)?._id;
  const selectedDate = useHMOStore((s) => s.selectedDate);

  const [date, setDate] = useState<string | null>(selectedDate!);

  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ["hospitalDoctors", hospitalId, date],
    queryFn: async () => await getHospitalDoctors(hospitalId!, date!),
    enabled: !!hospitalId && !!date,
  });
  const doctors = data || [];

  const { setValue } = useFormContext();

  if (error)
    return (
      <NoDataView
        title="Error"
        info={error?.message}
        onReload={refetch}
        buttonText="Reload"
        reloading={isLoading}
      />
    );

  return (
    <div>
      <BackButton step={hospitalSteps.SELECT_HOSPITAL} />

      <div>
        <h4 className="text-xl">Doctors in {hospitalName}</h4>
        <p className="text-xs text-[#666]">
          Select a Date and Doctor to Continue
        </p>

        <div className="mt-7">
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              value={date ? new Date(date) : null}
              onChange={(val) =>
                setDate(
                  val ? (isValidDate(val) ? val.toISOString() : null) : null,
                )
              }
              slots={{
                openPickerIcon: () => (
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                ),
              }}
              sx={{
                width: "100%",
                ".MuiInputBase-root": {
                  background: "#fff",
                  height: 48,
                  "--tw-shadow": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
                  "--tw-shadow-colored": "0 1px 2px 0 var(--tw-shadow-color)",
                  "box-shadow":
                    "var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)",

                  borderRadius: "calc(var(--radius) - 2px)",
                  border: "1px solid hsl(var(--input))",

                  fontSize: 12,
                  color: "hsl(var(--muted-foreground))",

                  "&:focus-within": {
                    borderColor: "var(--primary)",
                    borderWidth: "2px",
                  },

                  "& .MuiOutlinedInput-notchedOutline": {
                    border: "none",
                  },
                },
              }}
            />
          </LocalizationProvider>

          {isLoading ? (
            <div className="flex justify-center mt-7 ">
              <SpinnerLoader className="w-7" />
            </div>
          ) : doctors.length < 1 ? (
            <NoDataView title="No Doctor Available" info="" />
          ) : (
            <div className="mt-7 space-y-5">
              {doctors.map((doctor, idx) => {
                return (
                  <DoctorCard
                    key={idx}
                    doctor={doctor}
                    active={doctor?._id === selectedDocId}
                    setActive={() => {
                      setValue("doctor", doctor?._id);
                      setStoreValues({
                        selectedDoctor: doctor,
                      });
                      setFormStep(hospitalSteps.CREATE);
                    }}
                  />
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

async function getHospitalDoctors(hospitalProviderId: string, date: string) {
  const res = await restApi.get(
    `doctors?date=${date}&filterBy[providerId]=${hospitalProviderId}`,
  );

  return res.data?.data as Doctor[];
}
