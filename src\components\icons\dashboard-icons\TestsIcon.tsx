import { IconProps } from "@/lib/typescript/types";

export const TestsIcon: React.FC<IconProps> = ({ size, className }) => {
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_5641_57738)">
        <path d="M23.5612 4.4385L19.5612 0.438504C19.4218 0.299172 19.2564 0.188647 19.0744 0.113241C18.8923 0.0378345 18.6972 -0.000976562 18.5002 -0.000976562C18.1022 -0.000976562 17.7205 0.157109 17.4392 0.438504C17.1578 0.719899 16.9997 1.10155 16.9997 1.4995C16.9997 1.89746 17.1578 2.27911 17.4392 2.5605L18.3792 3.4995L17.0402 4.8385C15.9909 4.17784 14.7478 3.89422 13.5158 4.03436C12.2838 4.1745 11.1363 4.73005 10.2622 5.6095L5.18015 10.6825C4.48228 11.3771 3.92898 12.2031 3.55223 13.1128C3.17547 14.0225 2.98274 14.9979 2.98515 15.9825V18.8825L0.437152 21.4415C0.294251 21.5802 0.180403 21.746 0.102251 21.9292C0.0240992 22.1124 -0.016792 22.3093 -0.0180371 22.5084C-0.0192822 22.7076 0.0191438 22.905 0.0949992 23.0891C0.170855 23.2733 0.28262 23.4405 0.423777 23.581C0.564933 23.7215 0.732653 23.8324 0.917153 23.9074C1.10165 23.9824 1.29924 24.0199 1.49838 24.0177C1.69753 24.0155 1.89424 23.9737 2.07705 23.8947C2.25987 23.8157 2.42511 23.7011 2.56315 23.5575L5.10915 20.9995H8.00015C8.98525 21.002 9.96103 20.8089 10.8709 20.4312C11.7807 20.0535 12.6064 19.4989 13.3002 18.7995L18.3822 13.7265C19.2537 12.8503 19.8047 11.7061 19.9464 10.4785C20.0881 9.2508 19.8122 8.01119 19.1632 6.9595L20.5002 5.6205L21.4392 6.5605C21.7205 6.8419 22.1022 6.99998 22.5002 6.99998C22.8981 6.99998 23.2798 6.8419 23.5612 6.5605C23.8425 6.27911 24.0006 5.89746 24.0006 5.4995C24.0006 5.10155 23.8425 4.7199 23.5612 4.4385ZM16.2552 11.6095L11.1732 16.6825C10.7575 17.1007 10.263 17.4324 9.71842 17.6585C9.17381 17.8845 8.58981 18.0004 8.00015 17.9995H5.98515V15.9845C5.98955 15.4435 6.09117 14.9076 6.28515 14.4025L7.44215 15.5605C7.58148 15.6998 7.7469 15.8104 7.92894 15.8858C8.11099 15.9612 8.30611 16 8.50315 16C8.7002 16 8.89532 15.9612 9.07736 15.8858C9.25941 15.8104 9.42482 15.6998 9.56415 15.5605C9.70349 15.4212 9.81401 15.2558 9.88942 15.0737C9.96482 14.8917 10.0036 14.6966 10.0036 14.4995C10.0036 14.3025 9.96482 14.1073 9.88942 13.9253C9.81401 13.7432 9.70349 13.5778 9.56415 13.4385L8.11415 11.9915L9.99415 10.1145L11.4392 11.5605C11.7205 11.8419 12.1022 12 12.5002 12C12.8981 12 13.2798 11.8419 13.5612 11.5605C13.8425 11.2791 14.0006 10.8975 14.0006 10.4995C14.0006 10.1016 13.8425 9.7199 13.5612 9.4385L12.1172 7.9995L12.3822 7.7345C12.8575 7.28062 13.4894 7.02736 14.1467 7.02736C14.8039 7.02736 15.4358 7.28062 15.9112 7.7345L16.2542 8.0785C16.7217 8.54703 16.9844 9.18187 16.9846 9.8438C16.9848 10.5057 16.7225 11.1407 16.2552 11.6095Z" />
      </g>
      <defs>
        <clipPath id="clip0_5641_57738">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
