import { OTP } from "@/components/CustomOTPInput";
import { Message } from "@/components/Message";
import { useNavigate } from "@tanstack/react-router";
import {
  selectAccessEmail,
  selectSetAccessStep,
  useAccessStore,
} from "@/store/accessStore";
import {
  selectSetDashboardData,
  useDashboardStore,
} from "@/store/dashboardStore";

export const AccessOTPForm = () => {
  const email = useAccessStore(selectAccessEmail);
  const navigate = useNavigate({ from: "/access" });
  const setStep = useAccessStore(selectSetAccessStep);
  const setDashboardStore = useDashboardStore(selectSetDashboardData);

  return (
    <div className="w-full p-4 lg:p-10 space-y-10">
      <div className="space-y-10">
        <h1 className="text-4xl font-medium">Log in</h1>
        <Message
          message="We have sent you an email with your access code"
          type="info"
        />

        <OTP
          name="access"
          onComplete={(info) => {
            setDashboardStore({ healaId: info.healaId });
            sessionStorage.setItem("token", info.accessToken);
            setStep("email");
            navigate({ to: "/dashboard" });
          }}
          email={email}
          type="ACCESS"
        />
      </div>
    </div>
  );
};
