import { SVGProps } from "react";
const CircleCheckIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={101}
    height={100}
    viewBox="0 0 101 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="flex self-center"
    {...props}
  >
    <g filter="url(#filter0_b_7605_14614)">
      <rect x={0.5} width={100} height={100} rx={50} fill="#DEFEF3" />
    </g>
    <rect x={10.5} y={10} width={80} height={80} rx={40} fill="#3EA584" />
    <g clipPath="url(#clip0_7605_14614)">
      <path
        d="M46.2492 58.6626C45.5679 58.6629 44.9146 58.3921 44.4332 57.91L38.9431 52.4219C38.3523 51.8309 38.3523 50.873 38.9431 50.2821C39.534 49.6913 40.4919 49.6913 41.0828 50.2821L46.2492 55.4484L59.9172 41.7805C60.5081 41.1897 61.466 41.1897 62.0569 41.7805C62.6477 42.3714 62.6477 43.3293 62.0569 43.9202L48.0651 57.91C47.5838 58.3921 46.9305 58.6629 46.2492 58.6626Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_b_7605_14614"
        x={-32.7537}
        y={-33.2537}
        width={166.507}
        height={166.507}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation={16.6269} />
        <feComposite
          in2="SourceAlpha"
          operator="in"
          result="effect1_backgroundBlur_7605_14614"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_backgroundBlur_7605_14614"
          result="shape"
        />
      </filter>
      <clipPath id="clip0_7605_14614">
        <rect
          width={24}
          height={24}
          fill="white"
          transform="translate(38.5 38)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default CircleCheckIcon;
