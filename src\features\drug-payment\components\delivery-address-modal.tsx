import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AddressLabel } from "./address-label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { statesAndLgas } from "@/lib/states-and-lgas";
import { DeliveryAddress } from "../types";
import { AlertCircle } from "lucide-react";

interface DeliveryAddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (address: DeliveryAddress) => void;
}

export function DeliveryAddressModal({
  isOpen,
  onClose,
  onSave,
}: DeliveryAddressModalProps) {
  const [error, setError] = useState<string | null>(null);
  const [address, setAddress] = useState<DeliveryAddress>({
    street: "",
    city: "",
    landmark: "",
    state: "",
    lga: "",
  });



  // Clear errors and reset form when modal closes
  const handleClose = () => {
    setError(null);
    setAddress({
      street: "",
      city: "",
      landmark: "",
      state: "",
      lga: "",
    });
    onClose();
  };

  const handleUpdateAddress = (name: string, value: string) => {
    setError(null);

    if (!name || name === "") return;
    if (name === "state") {
      setAddress((prev) => ({
        ...prev,
        lga: "",
        [name]: value,
      }));
    } else {
      setAddress((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSave = () => {
    // Check if required fields are filled
    const missingFields = [];
    if (!address.state) missingFields.push("State");
    if (!address.lga) missingFields.push("LGA");
    if (!address.city) missingFields.push("City");
    if (!address.street) missingFields.push("Street");
    if (!address.landmark) missingFields.push("Landmark");

    if (missingFields.length > 0) {
      setError(`Please fill in the following required fields: ${missingFields.join(", ")}`);
      return;
    }

    onSave(address);
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Enter your delivery address</DialogTitle>
          <DialogDescription>
            This address will be used to deliver your drugs.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4 px-1 overflow-y-auto flex-1 min-h-0">

          <div>
            <AddressLabel htmlFor="street" className="mb-1 block" required>Street</AddressLabel>
            <Input
              id="street"
              value={address.street}
              onChange={(e) => handleUpdateAddress("street", e.target.value)}
              placeholder="Enter your street address"
            />
          </div>

          <div>
            <AddressLabel htmlFor="city" className="mb-1 block" required>City</AddressLabel>
            <Input
              id="city"
              value={address.city}
              onChange={(e) => handleUpdateAddress("city", e.target.value)}
              placeholder="Enter your city"
            />
          </div>

          <div>
            <AddressLabel htmlFor="state" className="mb-1 block" required>State</AddressLabel>
            <Select
              value={address.state}
              onValueChange={(value) => handleUpdateAddress("state", value)}
            >
              <SelectTrigger id="state" className="w-full">
                <SelectValue placeholder="Select a state" />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {Object.keys(statesAndLgas).map((state) => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <AddressLabel htmlFor="lga" className="mb-1 block" required>LGA</AddressLabel>
            <Select
              value={address.lga}
              onValueChange={(value) => handleUpdateAddress("lga", value)}
              disabled={!address.state}
            >
              <SelectTrigger id="lga" className="w-full">
                <SelectValue placeholder={address.state ? "Select LGA" : "Select a state first"} />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {(statesAndLgas[address.state] || []).map((lga) => (
                  <SelectItem key={lga} value={lga}>
                    {lga}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <AddressLabel htmlFor="landmark" className="mb-1 block" required>Closest Landmark</AddressLabel>
            <Input
              id="landmark"
              value={address.landmark || ""}
              onChange={(e) => handleUpdateAddress("landmark", e.target.value)}
              placeholder="Enter the closest landmark"
            />
          </div>

        </div>

        {/* Fixed footer with buttons */}
        <div className="flex-shrink-0 pt-4 border-t border-gray-200">
          {/* Error message display in footer for better visibility */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-700 font-medium">
                  {error}
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Address
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
