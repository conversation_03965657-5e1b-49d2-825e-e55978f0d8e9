import { useDoctorConsultationFormDataStore } from "@/features/doctor-consultation/hooks/useDoctorConsultationFormData";
import { useToast } from "@/hooks/use-toast";
import { API_KEY_LS_KEY } from "@/lib/constants";
import { restApi } from "@/lib/rest-api-client";
import { flattenFieldErrorsToSingleMessage } from "@/lib/utils";
import axios from "axios";
import { useCallback } from "react";

type ConsultationCreateData = {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  gender: "male" | "female";

  symptoms: { name: string }[];
  discomfortLevel: "None" | "Mild" | "Medium" | "Intense";
  firstNotice: "This week" | "Last week" | "One month ago";
  description?: string;

  date: Date;
  time: string;
  channel: "video" | "chat" | "voice";

  // use doctor fee for private doctor consultations and plan amount for hospital doctor consultations
  amount: number;

  doctorId: string;
  providerId: string;
  consultationType: "scheduled" | "instant";
  createdThrough:
    | "weblink"
    | "doctor-direct"
    | "app"
    | "api"
    | "video"
    | "hospital_direct";
};

export function useCreateConsultation() {
  const { toast } = useToast();

  const createConsultation = useCallback(
    async (data: ConsultationCreateData) => {
      const payload = {
        patientData: {
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          phoneNumber: data.phoneNumber,
          gender: data.gender,
        },

        symptoms: data.symptoms,
        discomfortLevel: data.discomfortLevel,
        firstNotice: data.firstNotice,
        description: data.description,

        time: getConsultationDateTime(data.date, data.time),
        contactMedium: data.channel,
        doctor: data.doctorId,
        providerId: data.providerId,
        type: data.consultationType,
        createdThrough: data.createdThrough,
        amount: data.amount,

        status: "pending",
        timeOffset: "0.00",
      };

      async function makeConsultationCreateApiCall() {
        try {
          const { data: consultationResData } = await restApi.post(
            "/consultations/create",
            payload,
          );

          const consultationInfo = consultationResData?.data;
          return {
            success: true as const,
            data: {
              email: consultationInfo?.patientData?.email,
              consultationId: consultationInfo?._id,
            },
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            return {
              success: false as const,
              errors: [
                {
                  field: "unknown",
                  message: error.response?.data?.message,
                },
              ],
            };
          }

          console.error("Unexpected error creating consultation: ", error);

          return {
            success: false as const,
            errors: [
              {
                field: "unknown",
                message: "An unexpected error occurred",
              },
            ],
          };
        }
      }

      async function createConsultationAndMakePayment() {
        const createConsultationResult = await makeConsultationCreateApiCall();
        if (createConsultationResult.success) {
          const consultationInfo = createConsultationResult.data;
          const callback_base_url = import.meta.env.DEV
            ? "https://new-weblinks.vercel.app"
            : window?.location?.origin;

          try {
            const apiKey = sessionStorage.getItem(API_KEY_LS_KEY);
            const paymentResponse = await restApi.post("/payments", {
              email: consultationInfo?.email,
              itemId: consultationInfo?.consultationId,
              reason: "consultation",
              callback_url: `${callback_base_url}/booking-success?consultationId=${consultationInfo?.consultationId}&apiKey=${apiKey}`,
              amount: data.amount,
            });
            const paymentURL =
              paymentResponse.data.data.paymentInitResponse.authorization_url;

            // clear form store
            useDoctorConsultationFormDataStore.getState().resetFormData();
            window.open(paymentURL, "_self");
            return;
          } catch (error) {
            if (axios.isAxiosError(error)) {
              const errorMessage = `Payment error: ${error.response?.data?.message}`;
              return {
                success: false as const,
                errors: [
                  {
                    field: "unknown",
                    message: errorMessage,
                  },
                ],
              };
            }

            console.error("Unexpected error creating consultation: ", error);

            return {
              success: false as const,
              errors: [
                {
                  field: "unknown",
                  message: "An unexpected error occurred",
                },
              ],
            };
          }
        } else {
          return {
            success: false as const,
            errors: createConsultationResult.errors,
          };
        }
      }

      const createConsultationAndPayResult =
        await createConsultationAndMakePayment();

      // if above succeeds, it redirects to payment page. Code below is
      // only executed when anything fails
      if (createConsultationAndPayResult?.success === false) {
        const errorMessage = flattenFieldErrorsToSingleMessage(
          createConsultationAndPayResult?.errors,
        );
        toast({
          title: "Error creating consultation",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [toast],
  );

  return {
    createConsultation,
  };
}

function getConsultationDateTime(date: Date, time: string) {
  const dateTime = new Date(date);
  const [hours, minutes] = time.split(":").map(Number);
  dateTime.setHours(hours);
  dateTime.setMinutes(minutes);
  return dateTime.toISOString();
}
