import * as React from "react";

import { cn } from "@/lib/utils";
import { useWidgetColor } from "@/hooks/useWidgetColor";

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  const widgetColor = useWidgetColor();
  return (
    <textarea
      style={{ outlineColor: widgetColor, outlineWidth: "2px" }}
      rows={5}
      className={cn(
        "flex min-h-[60px] w-full rounded-md border border-input bg-white px-3 py-2 text-base lg:text-xs shadow-sm placeholder:text-neutral-400 placeholder:text-xs focus-visible:outline disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = "Textarea";

export { Textarea };
