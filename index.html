<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="https://heala.ng/wp-content/uploads/2022/10/cropped-favicon-32x32.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Heala Virtual Health Platform</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.cdnfonts.com/css/euclid-circular-a?styles=100037,100036,100039,100031" rel="stylesheet">
    <link rel="apple-touch-icon" href="https://heala.ng/wp-content/uploads/2022/10/cropped-favicon-32x32.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      window.onerror = (event, source, lineno, colno, err) => {
        // must be within function call because that's when the element is defined for sure.
        const ErrorOverlay = customElements.get('vite-error-overlay');
        // don't open outside vite environment
        if (!ErrorOverlay) {
          return;
        }
        const overlay = new ErrorOverlay(err);
        document.body.appendChild(overlay);
      };
    </script>
  </body>
</html>
