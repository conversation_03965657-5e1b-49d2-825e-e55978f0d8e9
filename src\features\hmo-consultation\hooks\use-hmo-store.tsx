import { PlansResponse } from "@/features/consultation/types";
import {
  GetPartnerBySubdomainQuery,
  GetUserTypeProvidersQuery,
} from "@/graphql/generated/graphql";
import { create, StateCreator } from "zustand";
import { hospitalSteps, steps } from "../utils";
import {
  Consultation,
  Doctor,
  GetHMOUserResponse,
} from "@/features/hmo-consultation/types";
import { format } from "date-fns";
import { PersistOptions, persist, createJSONStorage } from "zustand/middleware";

type HMOStoreState = Partial<{
  plans: PlansResponse["data"];
  providerFeatures: {
    hasPharmacyFeature: boolean;
    hasFollowUpFeature: boolean;
    hasAddressFeature: boolean;
  };
  partnerInfo: GetPartnerBySubdomainQuery["getPartnerBySubdomain"];
  formStep: keyof typeof steps | keyof typeof hospitalSteps;
  userData: GetHMOUserResponse["data"][0];
  selectedPrevConsultation: Consultation;
  isHospitalConsultation: boolean;
  selectedHospital: NonNullable<
    GetUserTypeProvidersQuery["getUserTypeProviders"]["provider"]
  >[0];
  selectedDoctor: Doctor;
  selectedDate: string;
}>;

type HMOStoreActions = {
  setValues: (values: HMOStoreState) => void;
  setFormStep: (step: HMOStoreState["formStep"]) => void;
};

type HMOPartnerStore = HMOStoreState & HMOStoreActions;

type PersistsState = (
  config: StateCreator<HMOPartnerStore>,
  options: PersistOptions<HMOPartnerStore>,
) => StateCreator<HMOPartnerStore>;

// todo: add persist middleware
export const useHMOStore = create<HMOPartnerStore>(
  (persist as PersistsState)(
    (set) => {
      return {
        plans: undefined,
        providerFeatures: {
          hasFollowUpFeature: false,
          hasPharmacyFeature: false,
          hasAddressFeature: false,
        },
        partnerInfo: undefined,
        userData: undefined,
        formStep: /* steps?.INTRO_FORM || */ "INTRO_FORM",
        selectedPrevConsultation: undefined,
        isHospitalConsultation: false,
        selectedHospital: undefined,
        selectedDoctor: undefined,
        selectedDate: format(Date.now(), "yyyy-MM-dd"),

        setFormStep: (step) => set({ formStep: step }),
        setValues: (values) => set(values),
      };
    },
    {
      name: "hmo-store",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
