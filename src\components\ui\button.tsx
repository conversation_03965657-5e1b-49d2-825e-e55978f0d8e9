import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { healaColor } from "@/lib/mock-data";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-white shadow hover:opacity-90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        outlinePrimary:
          "border border-primary text-primary bg-background shadow-sm hover:bg-accent",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        tertiary:
          "bg-[rgb(34,34,34)] text-primary-foreground shadow hover:opacity-90",
        ghost: "text-primary hover:text-primary-300",
        link: "text-primary underline-offset-4 hover:underline",
        disabled:
          "cursor-not-allowed opacity-50 bg-primary text-primary-foreground hover:opacity-50",
      },
      size: {
        default: "h-12 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const ButtonSource = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);

const Button = React.forwardRef<
  HTMLButtonElement,
  ButtonProps & { loading?: boolean }
>((props, ref) => {
  const { variant } = props;
  const color = useWidgetColor();
  const normalizedVariant = variant ? variant : "default";

  // Dynamic styles
  const dynamicStyles = {
    backgroundColor:
      normalizedVariant === "default" || normalizedVariant === "disabled"
        ? color
        : undefined,
    borderColor:
      normalizedVariant === "outlinePrimary" || normalizedVariant === "outline"
        ? color
        : undefined,
    color:
      normalizedVariant === "outlinePrimary" ||
      normalizedVariant === "outline" ||
      normalizedVariant === "ghost" ||
      normalizedVariant === "link"
        ? color
        : undefined,
  };

  if (props.loading) {
    return (
      <ButtonSource ref={ref} style={dynamicStyles} {...props} disabled>
        <Loader2 className="animate-spin" />
      </ButtonSource>
    );
  } else {
    return <ButtonSource {...props} ref={ref} style={dynamicStyles} />;
  }
});

Button.displayName = "Button";

export { Button, buttonVariants };
