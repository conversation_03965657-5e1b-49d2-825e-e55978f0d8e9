import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function FirstNotice() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="firstNotice"
      render={({ field }) => (
        <FormItem>
          <FormLabel required>
            When did you start experiencing the symptoms?
          </FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select when you first noticed your symptoms" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="A few days ago">A few days ago</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
              <SelectItem value="One Month ago">
                One Month ago
              </SelectItem>
              <SelectItem value="Last Year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}