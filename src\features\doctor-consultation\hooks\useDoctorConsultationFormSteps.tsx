import { createContext, useContext, useState } from "react";

const formSteps = [
  "symptom-information",
  "booking-preferences",
  "personal-information",
] as const;

type FormStep = (typeof formSteps)[number];

type FormStepsProviderProps = {
  children: React.ReactNode;
};

type Context = {
  currentStep: FormStep | null;
  setCurrentStep: (step: FormStep) => void;
  goBack: () => void;
  hasPreviousStep: boolean;
};

const FormStepsContext = createContext<Context>({
  currentStep: null,
  setCurrentStep: () => {},
  goBack: () => {},
  hasPreviousStep: false,
});

export function FormStepsProvider(props: FormStepsProviderProps) {
  const { children } = props;
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const hasPreviousStep = currentStepIndex !== 0;

  function goBack() {
    setCurrentStepIndex(Math.max(0, currentStepIndex - 1));
  }

  function setCurrentStep(step: FormStep) {
    setCurrentStepIndex(formSteps.findIndex((s) => s === step));
  }

  return (
    <FormStepsContext.Provider
      value={{
        currentStep: formSteps[currentStepIndex],
        hasPreviousStep,
        goBack,
        setCurrentStep,
      }}
    >
      {children}
    </FormStepsContext.Provider>
  );
}

export function useDoctorConsultationFormSteps() {
  const steps = useContext(FormStepsContext);

  return steps;
}
