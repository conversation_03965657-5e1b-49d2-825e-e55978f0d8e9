import { SVGProps } from "react";
const VideoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    className="conmainsult-success"
    xmlns="http://www.w3.org/2000/svg"
    width={19}
    height={18}
    viewBox="0 0 19 18"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_1640_68930)">
      <path
        d="M17.675 4.46723C17.1684 4.21052 16.56 4.26403 16.106 4.60522L14.7312 5.62599C14.5363 3.71095 12.9249 2.25341 11 2.25098C11.375 2.25036 11.5971 2.24912 11 2.25098H4.25001C3.8358 2.25098 3.875 2.25037 4.25001 2.25098C3.8358 2.25098 4.25001 2.6652 4.25001 2.25098C2.17998 2.25348 0.502496 3.93096 0.5 6.001V12.001C0.502496 14.071 2.17998 15.7485 4.25001 15.751H11C12.925 15.7486 14.5363 14.291 14.7312 12.376L16.1038 13.399C16.7665 13.896 17.7067 13.7617 18.2037 13.099C18.3985 12.8394 18.5037 12.5236 18.5037 12.199V5.80824C18.5057 5.23979 18.1843 4.71973 17.675 4.46723ZM13.25 12.001C13.25 13.2436 12.2426 14.251 11 14.251H4.25001C3.00738 14.251 2.00001 13.2436 2.00001 12.001V6.001C2.00001 4.75836 3.00738 3.751 4.25001 3.751H11C12.2426 3.751 13.25 4.75836 13.25 6.001V12.001ZM17 12.1937L14.75 10.5167V7.48526L17 5.80827V12.1937Z"
        fill="#5D626C"
      />
    </g>
    <defs>
      <clipPath id="clip0_1640_68930">
        <rect width={18} height={18} fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);
export default VideoIcon;
