import { useRef, useState } from "react";
import { Chip } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { cn, convertToSentenceCase } from "@/lib/utils";
import { useQuery as useApolloQuery } from "@apollo/client";
import { gql } from "@/graphql/generated";
import { GetIssuesQuery } from "@/graphql/generated/graphql";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FormSchemaType } from "@/features/hmo-consultation/schema";

// const isMobile = isBelowLaptopSize();
// const infoTextForPCs = "Click enter to input multiple symptoms";
// const infoTextForMobile = "Separate symptoms with a comma (,)";
// const infoText = "Click enter/return to input multiple symptoms or separate symptoms with a comma (,)";

type Issue = NonNullable<GetIssuesQuery["getIssues"]["issues"]>[0];

// todo: rename, remove duplicate, expose value / onchange props and re-use in other places
export const SymptomsSelectorInput2 = () => {
  const [symptom, setSymptom] = useState("");
  const [filteredIssues, setFilteredIssues] = useState<Issue[]>([]);

  const { setValue, getValues } = useFormContext<FormSchemaType>();

  const { data } = useApolloQuery(issuesQuery);
  const issues = data?.getIssues.issues || [];

  const addSymptom = (symptom: string) => {
    if (symptom === "" || symptom === " ") {
      return;
    }

    const trimmedSymptom = symptom.trim();
    const symptomArr = trimmedSymptom.split(/[,]+/);

    for (let index = 0; index < symptomArr.length; index++) {
      const symptom = convertToSentenceCase(`${symptomArr[index]}`);
      setFilteredIssues([]);
      setSymptom("");
      const prevSymptoms = getValues("symptoms") ? getValues("symptoms") : [];
      setValue("symptoms", [{ name: symptom }, ...prevSymptoms], {
        shouldValidate: true,
      });
    }
  };

  // Handle typing in symptoms input
  const handleSymptomChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;

    if (value === "" || value === " ") {
      setSymptom("");
      setFilteredIssues([]);
      return;
    }

    const symptomStringCase = convertToSentenceCase(value);

    if (value[value.length - 1] === ",") {
      const valueWithoutComma = `${value}`.replace(/,/g, "");
      addSymptom(valueWithoutComma);
      return;
    }

    setSymptom(symptomStringCase);

    const filtered = issues.filter((issue) =>
      issue.Name?.includes(symptomStringCase),
    );
    setFilteredIssues(filtered);
  };

  const removeSymptom = (id: number) => {
    const prevSymptoms = getValues("symptoms") ? getValues("symptoms") : [];
    const filteredSymptoms = (prevSymptoms || []).filter(
      (_, idx) => idx !== id,
    );
    setValue("symptoms", filteredSymptoms);
  };

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div>
      <div className="mb-1">
        <div className="flex gap-2">
          <div className="flex flex-col gap-1 flex-1">
            <Input
              type="text"
              name="name"
              autoComplete="off"
              id="symptoms-input"
              placeholder="Enter Symptoms"
              value={symptom}
              onChange={handleSymptomChange}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.stopPropagation();
                  e.preventDefault();
                }
              }}
              onKeyUp={(e) => {
                const isEmpty = !symptom || symptom === "";
                if (e.key === "Enter") {
                  if (isEmpty) return;
                  setSymptom(symptom);
                  setFilteredIssues([]);
                  // @ts-expect-error for some weird reason, the event target object doesn't have the 'value' prop in TypeScript but works at runtime
                  addSymptom(e.target.value);
                }
              }}
              ref={inputRef}
            />
            {filteredIssues.length > 0 && (
              <div className={issuesSelectStyle}>
                {filteredIssues.map((item) => (
                  <div
                    key={item.ID}
                    className={issuesSelectItemStyle}
                    onClick={() => {
                      inputRef.current?.focus();
                      addSymptom(item?.Name || "");
                    }}
                  >
                    {item.Name}
                  </div>
                ))}
              </div>
            )}
          </div>
          <Button
            type="button"
            onClick={() => addSymptom(symptom)}
            className="h-12 text-xs"
          >
            Add
          </Button>
        </div>
      </div>
      <div className="flex flex-wrap gap-1 max-w-[450px] mt-2">
        {(getValues("symptoms") || []).map((symptom, idx) => (
          <Chip
            label={symptom?.name}
            onDelete={() => {
              removeSymptom(idx);
            }}
            key={idx}
          />
        ))}
      </div>
    </div>
  );
};

const issuesQuery = gql(`
  query getIssues {
    getIssues {
      issues {
        ID
        Name
      }
    }
  }
`);

const issuesSelectStyle = cn(
  "p-1 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
);
const issuesSelectItemStyle = cn(
  "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent hover:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
);
