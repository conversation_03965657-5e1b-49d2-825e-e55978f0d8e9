import { Head } from "@/components/head";
import HealaLogoIcon from "@/components/icons/heala-logo-icon";
import { Button } from "@/components/ui/button";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { hospitalSteps, steps } from "@/features/hmo-consultation/utils";
import {
  createFileRoute,
  useNavigate,
  useSearch,
} from "@tanstack/react-router";

const hospitalConsultationWhiteList = (
  import.meta.env.VITE_APP_HOSPITAL_CONSULT_WHITELIST || ""
).split(" ");

export const Route = createFileRoute("/hmo/$subdomain/")({
  component: RouteComponent,
});

function RouteComponent() {
  // todo: useHMOStore with selector as this pattern causes unneccessary re-renders
  const { partnerInfo, setValues: setStoreValues } = useHMOStore();
  const { widgetLogo, subdomain, providerId } = partnerInfo || {};

  const searchParams = useSearch({ from: "/hmo/$subdomain/" });
  // @ts-expect-error property via does not exist. need to add 'via' to search params
  const via = searchParams.via;

  const navigate = useNavigate({ from: Route.id });

  const hasHospitalConsultationFeature =
    hospitalConsultationWhiteList.includes(providerId);

  return (
    <>
      <Head
        title={subdomain || undefined}
        faviconURL={widgetLogo || undefined}
        withoutSuffix
      />
      <div className="flex flex-col sm:w-[70%] lg:max-w-[1076px] mx-auto px-4 pt-12">
        {/* logo */}
        <div className="flex justify-center">
          {widgetLogo ? (
            <img
              src={widgetLogo}
              className="max-h-[64px] max-w-[160px] md:max-w-[200px] object-contain"
              width={160}
              height={45}
            />
          ) : (
            <HealaLogoIcon className="w-16 h-16" />
          )}
        </div>

        {/* hospital cover image */}
        <div className="h-[172px] lg:h-[300px] mt-12 rounded-md overflow-hidden">
          <img
            className="object-cover h-full w-full"
            src="/hospital-landing-banner.png"
            alt="Healthy family with kid smilling"
          />
        </div>

        <div className="text-center mt-12 ">
          <div className="max-w-[356px] mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold">Talk to a Doctor</h2>
            <p className="max-w-[276px] mx-auto mt-6 text-[#A6A6A6] text-sm">
              Experience the comfort of receiving medical advice from your home.
              Available 24/7.
            </p>
          </div>

          <div className="mt-12 pb-12 w-full ">
            <div className="flex flex-col items-center justify-center lg:flex-row gap-6 lg:gap-4 max-w-[780px] mx-auto ">
              <Button
                className="w-full max-w-[360px]"
                onClick={() => {
                  setStoreValues({
                    isHospitalConsultation: false,
                    formStep: steps.INTRO_FORM,
                  });
                  navigate({
                    to: "/hmo/$subdomain/create-consultation",
                    params: { subdomain: subdomain! },
                    // @ts-expect-error fix type
                    state: { via },
                  });
                }}
              >
                Start Instant Consultation
              </Button>

              {hasHospitalConsultationFeature && (
                <Button
                  variant="outlinePrimary"
                  className="w-full max-w-[360px]"
                  // disabled={!plans?.length}
                  onClick={() => {
                    setStoreValues({
                      isHospitalConsultation: true,
                      formStep: hospitalSteps.INTRO_FORM,
                    });
                    navigate({
                      to: "/hmo/$subdomain/create-consultation",
                      params: { subdomain: subdomain! },
                      // @ts-expect-error fix type
                      state: { via },
                    });
                  }}
                >
                  Book Hospital Appointment
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
