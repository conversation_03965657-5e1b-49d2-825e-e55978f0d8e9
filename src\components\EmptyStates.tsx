import noData from "@/assets/no-data.svg";
import error from "@/assets/error-svg.svg";
import React from "react";

export const NoData = ({
  text,
  isError,
  info,
}: {
  isError: boolean;
  text: string | React.ReactNode;
  info?: string | React.ReactNode;
}) => {
  const img = isError ? error : noData;
  const imgAlt = isError ? "error" : " no data";

  return (
    <span className="flex flex-col justify-center items-center text-center">
      <img src={img} className="w-[250px]" alt={imgAlt} />
      <span className="space-y-4">
        <h3 className="text-black font-semibold text-xl">{text}</h3>
        <span className="text-gray-500 text-sm">{info}</span>
      </span>
    </span>
  );
};
