import { PlansResponse } from "@/features/consultation/types";
import { restApi } from "@/lib/rest-api-client";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";

type usePartnerPlansDataProps = {
  providerId: string | undefined | null;
};

export function usePartnerPlansData(props: usePartnerPlansDataProps) {
  const { providerId } = props;
  const { data, isLoading, error } = useQuery({
    queryKey: ["plans"],
    queryFn: () => fetchPlans(providerId!),
    enabled: !!providerId,
  });

  useEffect(() => {
    if (error) {
      console.log("Error fetching plans: ", error);
    }
  }, [error]);

  return {
    plansData: data?.data.data,
    loading: isLoading,
    error,
  };
}

const fetchPlans = (providerId: string) => {
  return restApi.get<PlansResponse>(`plans?filterBy[provider]=${providerId}`);
};
