import classNames from "classnames";
import { TestsIcon } from "../icons/dashboard-icons/TestsIcon";
import { ConsultationIcon } from "../icons/dashboard-icons/consultationIcon";
import { PrescriptionsIcon } from "../icons/dashboard-icons/prescriptionsIcon";
import { Skeleton } from "../ui/skeleton";

type CardType = "consultations" | "prescriptions" | "tests";

const icons = {
  consultations: ConsultationIcon,
  tests: TestsIcon,
  prescriptions: PrescriptionsIcon,
};

const getIcon = (type: CardType) => icons[type];

export const DashboardOverviewCard = ({
  count,
  text,
  type,
  loading,
}: {
  count: number | string;
  text: string;
  type: CardType;
  loading: boolean;
}) => {
  const Icon = getIcon(type);

  return (
    <div
      className={classNames(
        "w-full flex justify-between items-center border border-[#EDEDED] rounded-lg",
      )}
    >
      {loading ? (
        <div className="w-full h-[116px] flex items-center justify-between space-x-4 p-5">
          <div className="space-y-4">
            <Skeleton className="h-4 w-[50px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
          <Skeleton className="h-14 w-14 rounded-full" />
        </div>
      ) : (
        <>
          <div
            className={classNames("w-[7px] h-full rounded-l-full", {
              "bg-[#1A7ABF]": type === "consultations",
              "bg-[#3EA584]": type === "prescriptions",
              "bg-[#EEB76B]": type === "tests",
            })}
          ></div>
          <div className="w-full flex justify-between items-center p-7">
            <div className="space-y-1">
              <p className="text-2xl text-[#14142B] font-medium">{count}</p>
              <p className="text-[#14142B]">{text}</p>
            </div>
            <div
              className={classNames("rounded-full p-4 bg-opacity-10", {
                "bg-[#1A7ABF]": type === "consultations",
                "bg-[#3EA584]": type === "prescriptions",
                "bg-[#EEB76B]": type === "tests",
              })}
            >
              <Icon
                className={classNames("", {
                  "fill-[#1A7ABF]": type === "consultations",
                  "fill-[#3EA584]": type === "prescriptions",
                  "fill-[#EEB76B]": type === "tests",
                })}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};
