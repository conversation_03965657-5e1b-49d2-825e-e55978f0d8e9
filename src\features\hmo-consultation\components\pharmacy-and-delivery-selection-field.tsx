import { Card } from "@/components/ui/card";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { GetPharmaciesFromWellaHealthQuery } from "@/graphql/generated/graphql";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { cn, getAutoCompleteStyles } from "@/lib/utils";
import { Autocomplete, styled, TextField } from "@mui/material";
// import { AlertTriangle } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useFormContext } from "react-hook-form";

const deliveryFeatureWhiteList = (
  import.meta.env.VITE_APP_DELIVERY_FEATURE_WHITELIST || ""
).split(" ");

const nemHMO = (import.meta.env.VITE_APP_NEM_HMO || "").split(" ");

const defaultPharmacy = {
  pharmacyName: "--Select Pharmacy--",
  pharmacyCode: null,
  address: null,
  area: null,
  lga: null,
  state: null,
};

type PharmacyAndDeliverySelectionFieldProps = {
  loadingPharmacies: boolean;
  pharmacies:
    | GetPharmaciesFromWellaHealthQuery["getPharmaciesFromWellaHealth"]["data"]
    | undefined;
};

export function PharmacyAndDeliverySelectionField(
  props: PharmacyAndDeliverySelectionFieldProps,
) {
  const widgetColor = useWidgetColor();
  const { loadingPharmacies, pharmacies } = props;
  const appState = useHMOStore();
  const providerId = appState?.partnerInfo?.providerId;

  const pharmacyArr = useMemo(() => [...(pharmacies || [])], [pharmacies]);

  const { control, watch, getValues, setValue } =
    useFormContext<FormSchemaType>();

  const isNemHMO = nemHMO.includes(providerId);

  const canSelectDeliveryMode = deliveryFeatureWhiteList.includes(providerId);
  const watchPharmacyCode = watch("pharmacyCode");

  useEffect(() => {
    const pharmacyCode = getValues()?.pharmacyCode;
    const noPharmacySelected = !pharmacyCode || pharmacyCode === "";

    const foundPharmacy = (pharmacyArr || []).filter(
      (pharmacy) => pharmacyCode === pharmacy?.pharmacyCode,
    )[0];

    const pharmName = noPharmacySelected ? null : foundPharmacy?.pharmacyName;
    setValue("pharmacyName", pharmName || undefined);
    const { address, area, lga, state } = foundPharmacy || {};
    const pharmacyAddress = noPharmacySelected
      ? null
      : `${address}, ${area}, ${lga}, ${state}.`;
    setValue("pharmacyAddress", pharmacyAddress || undefined);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchPharmacyCode]);

  useEffect(() => {
    if (!canSelectDeliveryMode) {
      setValue("isDelivery", false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canSelectDeliveryMode]);

  const selectedPharmacyCode = watch("pharmacyCode");

  const selectedOption = useMemo(() => {
    const filteredPharmacyArr = (pharmacyArr || []).filter(
      (pharm) => pharm?.pharmacyCode === selectedPharmacyCode,
    );

    return filteredPharmacyArr?.length > 0
      ? filteredPharmacyArr[0]
      : defaultPharmacy;
  }, [pharmacyArr, selectedPharmacyCode]);

  return (
    <div>
      <FormLabel required>Select Your Preferred Pickup Pharmacy</FormLabel>
      <FormDescription>
        Selecting a pharmacy closest to you will help us process your drugs
        faster.
      </FormDescription>
      {/* Pharmacy selector */}
      <Autocomplete
        id="pharmacy-autocomplete"
        className="mt-1"
        loading={loadingPharmacies}
        clearOnBlur={true}
        disableClearable={true}
        options={pharmacyArr}
        sx={getAutoCompleteStyles(widgetColor, false)}
        value={
          selectedOption
            ? (pharmacyArr || []).find((option) => {
                return option.pharmacyCode === selectedOption.pharmacyCode;
              })
            : undefined
        }
        isOptionEqualToValue={(option, value) =>
          option?.pharmacyCode === value?.pharmacyCode
        }
        getOptionLabel={(option) => option?.pharmacyName || ""}
        onChange={(_, newValue) => {
          setValue("pharmacyCode", newValue?.pharmacyCode || "");
        }}
        renderInput={(params) => (
          <StyledTextField
            {...params}
            InputLabelProps={{ shrink: false }}
            placeholder="Type to search by name"
          />
        )}
        renderOption={(props, option) => (
          <PharmacyItem pharmacy={option} autoCompleteItemProps={props} />
        )}
      />

      {isNemHMO && (
        <Card className="outline outline-blue-400 bg-blue-100 text-blue-900 p-4 flex items-start gap-4 mt-5 rounded-lg">
          <div>
            <p className="text-sm">
              Please note that your selected pharmacy can only be changed if
              your medication is unavailable.
              {/* After you’ve chosen a pharmacy and completed your consultation, it
              can’t be changed — unless the pharmacy doesn’t have your
              medication, then we’ll reroute your order. */}
            </p>
          </div>
        </Card>
      )}

      {canSelectDeliveryMode && (
        <FormField
          control={control}
          name="isDelivery"
          render={({ field }) => {
            return (
              <FormItem className="space-y-3 mt-3">
                <FormLabel>Select fulfillment option</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) =>
                      value === "true"
                        ? field.onChange(true)
                        : field.onChange(false)
                    }
                    value={
                      field.value === true
                        ? "true"
                        : field.value === false
                          ? "false"
                          : undefined
                    }
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value={"false"} />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Pharmacy Pickup
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value={"true"} />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Home Delivery
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      )}
    </div>
  );
}

type PharmacyItemProps = {
  pharmacy: GetPharmaciesFromWellaHealthQuery["getPharmaciesFromWellaHealth"]["data"][0];
  autoCompleteItemProps: React.HTMLAttributes<HTMLLIElement> & {
    key: string;
  };
};

function PharmacyItem(props: PharmacyItemProps) {
  const color = useWidgetColor();
  const { pharmacy, autoCompleteItemProps: autoCompleteItemProps } = props;
  const { getValues } = useFormContext<FormSchemaType>();

  const pharmName = pharmacy?.pharmacyName ? pharmacy?.pharmacyName : "No name";
  const pharmAddress = pharmacy?.address ? `${pharmacy?.address}, ` : "";

  const pharmArea = pharmacy?.area ? `${pharmacy?.area}, ` : "";
  const pharmLGA = pharmacy?.lga ? `${pharmacy?.lga}, ` : "";
  const pharmState = pharmacy?.state ? `${pharmacy?.state} state.` : "";
  const pharmCode = pharmacy?.pharmacyCode;
  const isSelected = pharmCode === getValues()?.pharmacyCode;

  return (
    <li
      {...autoCompleteItemProps}
      style={isSelected ? { backgroundColor: color } : {}}
      className={cn("!px-3 !py-2 border-b cursor-pointer hover:bg-gray-50")}
    >
      <p
        style={isSelected ? { color: "#ffffff" } : { color }}
        className={cn("text-sm font-bold")}
      >
        {pharmName}
      </p>
      <p
        style={isSelected ? { color: "#fff" } : { color: "#0c1322" }}
        className="text-xs text-gr max-w-[80vw] lg:max-w-[400px]"
      >
        {`${pharmAddress}${pharmArea}${pharmLGA}${pharmState}`}
      </p>
    </li>
  );
}

const StyledTextField = styled(TextField)(() => ({
  "& .MuiInputBase-root": {
    // backgroundColor: "white",
    // height: 36,
    // border: "1px solid hsl(var(--input))",
    // borderRadius: "6px",
    // padding: "4px 12px",
    fontSize: { xs: "16px", sm: "16px", md: "16px", lg: "12px", xl: "12px" },

    "--tw-shadow": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    "--tw-shadow-colored": "0 1px 2px 0 var(--tw-shadow-color)",
    boxShadow:
      "var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)",
  },

  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },

  "& .MuiInputBase-input": {
    padding: 0,
    color: "hsl(var(--muted-foreground))",
  },
}));
