import { useQuery as useApolloQuery } from "@apollo/client";
import { getPartnerBySubdomainQuery } from "@/graphql/queries";

type UsePartnerDataProps = {
  subdomain: string;
};

export function usePartnerData(props: UsePartnerDataProps) {
  const { subdomain } = props;

  const {
    data: partnerData,
    loading,
    error,
  } = useApolloQuery(getPartnerBySubdomainQuery, { variables: { subdomain } });
  const providerId = partnerData?.getPartnerBySubdomain.providerId;
  const apiKey = partnerData?.getPartnerBySubdomain.apiKey;

  return {
    loading,
    error,
    providerId,
    apiKey,
    partnerData: partnerData?.getPartnerBySubdomain,
  };
}
