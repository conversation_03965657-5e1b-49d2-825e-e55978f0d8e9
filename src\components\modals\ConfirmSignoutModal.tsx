import { Button } from "../ui/button";
import { gql } from "@/graphql/generated";
import { useMutation } from "@apollo/client";
import { Dialog } from "@radix-ui/react-dialog";
import { useNavigate } from "@tanstack/react-router";
import { DialogContent, DialogTitle } from "../ui/dialog";

const SIGN_OUT = gql(`
  mutation signOutDashboard {
    logout
  }
`);

export const ConfirmSignoutModal = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const navigate = useNavigate({ from: "/dashboard" });
  const [signOut, { loading }] = useMutation(SIGN_OUT, {
    onCompleted: () => completeSignOut(),
    onError: () => completeSignOut(),
  });

  const completeSignOut = () => {
    setOpen(false);
    sessionStorage.clear();
    localStorage.clear();
    navigate({ to: "/access" });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="space-y-2">
        <DialogTitle className="text-center font-medium lg:text-xl">
          Are you sure you want to Sign out?
        </DialogTitle>

        <div className="flex flex-col md:flex-row justify-between mt-5 space-y-3 md:space-y-0 md:space-x-5">
          <Button
            className="flex-1"
            variant="default"
            onClick={() => setOpen(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            className="flex-1"
            variant="destructive"
            onClick={() => signOut()}
            loading={loading}
            disabled={loading}
          >
            Sign out
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
