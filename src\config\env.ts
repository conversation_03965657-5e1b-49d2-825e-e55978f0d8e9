import * as z from "zod";

const createEnv = () => {
  const EnvSchema = z.object({
    API_BASE_URL: z.string(),
    HEALA_PROVIDER_ID: z.string(),
    HEALA_PROVIDER_API_KEY: z.string().min(1),
    CONSULTATION_BASE_URL: z.string().url(),
    MODE: z.string(),
  });

  const envVars = Object.entries(import.meta.env).reduce<
    Record<string, string>
  >((acc, curr) => {
    const [key, value] = curr;
    if (key.startsWith("VITE_APP_")) {
      acc[key.replace("VITE_APP_", "")] = value;
    }
    return acc;
  }, {});

  const parsedEnv = EnvSchema.safeParse(envVars);

  if (!parsedEnv.success) {
    throw new Error(
      `Invalid env provided.
The following variables are missing or invalid:
${Object.entries(parsedEnv.error.flatten().fieldErrors)
  .map(([k, v]) => `- ${k}: ${v}`)
  .join("\n")}
`,
    );
  }

  return parsedEnv.data;
};

export const env = createEnv();
