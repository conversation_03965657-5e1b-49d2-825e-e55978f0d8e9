import CircleCheckIcon from "@/components/icons/circle-check-icon";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { restApi } from "@/lib/rest-api-client";
import { convertToInternationalFormat } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { useState } from "react";

type GetEnrolleeIdModalProps = {
  open: boolean
  onClose: () => void
}

export function GetEnrolleeIdModal(props: GetEnrolleeIdModalProps) {
  const { open, onClose } = props

  const appState = useHMOStore();
  const [value, setValue] = useState("");
  const { providerId } = appState?.partnerInfo || {};

  const {
    isPending,
    isSuccess,
    error,
    mutate: getEnrolleId
  } = useMutation({
    mutationFn: () => GET_ENROLLEE_ID(providerId!, value)
  });


  return (
    <Dialog open={open} onOpenChange={open ? onClose : undefined}>
      <DialogContent className="max-w-[370px]">
        {
          isSuccess ? (
            <div className="flex flex-col gap-2 justify-center items-center">
              <CircleCheckIcon />
              <p className="text-center">Your enrollee/Staff ID has been sent to your phone number</p>
            </div>
          ) : (
            <div>
              <DialogHeader>
                <DialogTitle>
                  Retrieve your enrollee ID
                </DialogTitle>
                <DialogDescription>
                  Enter your phone number and we will send your enrollee ID/policy
                  number to you.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-2 mt-6 ">
                {error && <p className="text-xs text-red-500">{error.message}</p>}

                <Input
                  name="phoneNumber"
                  onChange={(e) => setValue(e.target.value)}
                />

                <Button
                  loading={isPending}
                  onClick={() => getEnrolleId()}
                  className="w-full"
                >Submit</Button>
              </div>

            </div>)}
      </DialogContent>
    </Dialog>
  )
}

async function GET_ENROLLEE_ID(providerId: string, phone: string) {
  try {
    return await restApi.post(`enrollees/send-sms`, {
      providerId,
      phone: convertToInternationalFormat(phone, 'NG').replace(/\s+/g, '')
    })
  } catch (e) {
    console.error('Error retrieving Enrollee ID: ', e);

    if (isAxiosError(e)) {
      if (e.response?.data.statusCode === 404) {
        throw new Error("Phone number does not exist.")
      }

      throw new Error("Something went wrong")
    } else {
      throw e
    }
  }
}