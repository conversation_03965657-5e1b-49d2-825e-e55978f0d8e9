import { SymptomsSelectorInput2 } from "@/components/symptom-selector-input-2"
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function SymptomsField() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="symptoms"
      render={({ field }) => (
        <FormItem>
          <FormLabel>What are your symptoms?</FormLabel>
          <FormDescription>Click Enter or Add to input multiple symptoms or separate with commas (,).</FormDescription>
          <FormControl className="w-full">
            <SymptomsSelectorInput2 />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

