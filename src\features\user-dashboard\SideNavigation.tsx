import { useMemo, useState } from "react";
import classNames from "classnames";
import { MenuIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { navigationItems } from "@/lib/mock-data";
import { formatName, getInitials } from "@/lib/utils";
import { useScreenSize } from "@/hooks/useScreenSize";
import healaWordMark from "@/assets/heala-workmark.svg";
import HealaLogo from "@/components/icons/heala-logo-icon";
import { Link, useRouterState } from "@tanstack/react-router";
import { ConfirmSignoutModal } from "@/components/modals/ConfirmSignoutModal";
import { selectDashboardData, useDashboardStore } from "@/store/dashboardStore";

export const SideNavigation = () => {
  const screenSize = useScreenSize();
  const routerState = useRouterState();
  const pathname = routerState.location.pathname;
  const profile = useDashboardStore(selectDashboardData)?.profile;
  const isTablet = useMemo(() => screenSize.width < 1240, [screenSize.width]);
  const [mode, setMode] = useState<"icon" | "full">(isTablet ? "icon" : "full");

  const show = mode !== "icon";

  const [confirmModal, setConfirmModal] = useState(false);

  return (
    <aside
      className={classNames(
        " h-screen flex flex-col  justify-between overflow-y-auto  transform transition-all ease-in-out duration-200 border-r",
        { "w-[260px]": show, "w-16": !show },
      )}
    >
      <div className="h-full flex flex-col">
        <div className="flex-1">
          {/* AVATAR */}
          <div className="space-y-1 flex flex-col items-center justify-center my-8">
            {profile?.image ? (
              <img
                src={profile.image}
                alt={`Image of ${profile.firstName} ${profile.lastName}`}
                className={classNames(" rounded-full", {
                  "h-20 w-20": show,
                  "h-14 w-14": !show,
                })}
              />
            ) : (
              <p
                className={classNames(
                  "flex items-center justify-center font-medium text-xl text-gray-600 rounded-full bg-gray-300",
                  {
                    "h-20 w-20": show,
                    "h-14 w-14": !show,
                  },
                )}
              >
                {getInitials(`${profile?.firstName} ${profile?.lastName}`)}
              </p>
            )}

            {show && (
              <>
                <p className="text-black">
                  {formatName(profile.firstName, profile.lastName)}
                </p>
                <p className="text-black text-sm">{profile.email}</p>
                <Button variant="ghost">Edit Profile</Button>
              </>
            )}
          </div>
          {/*  LOGO */}
          <div className="flex items-center justify-center">
            {(isTablet || mode === "icon") && (
              <div className="flex justify-center">
                <button
                  onClick={() =>
                    setMode((prev) => {
                      if (prev === "full") return "icon";
                      if (prev === "icon") return "full";
                      return "icon";
                    })
                  }
                  className="w-4/5 group flex-1 flex justify-center items-center border border-black hover:border-primary rounded-lg lg:px-3 py-3 my-7"
                >
                  <MenuIcon
                    size={20}
                    className="text-black group-hover:text-primary"
                  />
                </button>
              </div>
            )}
          </div>

          {/* NAVIGATION */}
          <ul className="space-y-5">
            {navigationItems.map((item, idx) => {
              const isActive = pathname === item.path;

              return item.name === "Sign out" ? (
                <li
                  key={`${item.name}-${idx}`}
                  className={classNames(
                    "group w-4/5 flex items-center text-sm space-x-4 cursor-pointer rounded-lg mx-auto p-3 hover:bg-[rgba(62,94,169,0.1)] hover:text-primary transform transition ease-in-out duration-300",
                    {
                      "text-primary bg-[rgba(62,94,169,0.1)]": isActive,
                      "text-black border-transparent": !isActive,
                    },
                  )}
                  title={show ? "" : item?.name}
                  onClick={() => setConfirmModal(true)}
                >
                  <item.icon
                    className={classNames("group-hover:fill-primary ", {
                      "fill-primary": isActive,
                      "fill-black": !isActive,
                    })}
                  />
                  {show && <span>{item.name}</span>}
                </li>
              ) : (
                <Link
                  key={`${item.name}-${idx}`}
                  to={item?.path}
                  className="block"
                >
                  <li
                    className={classNames(
                      "group w-4/5 flex items-center text-sm space-x-4 cursor-pointer rounded-lg mx-auto p-3 hover:bg-[rgba(62,94,169,0.1)] hover:text-primary transform transition ease-in-out duration-300",
                      {
                        "text-primary bg-[rgba(62,94,169,0.1)]": isActive,
                        "text-black border-transparent": !isActive,
                      },
                    )}
                    title={show ? "" : item?.name}
                  >
                    <item.icon
                      className={classNames("group-hover:fill-primary ", {
                        "fill-primary": isActive,
                        "fill-black": !isActive,
                      })}
                    />
                    {show && <span>{item.name}</span>}
                  </li>
                </Link>
              );
            })}
          </ul>
        </div>

        <div className="flex flex-col items-center mb-5">
          {show && <p className="text-sm text-black">Powered By:</p>}
          {show ? (
            <img
              src={healaWordMark}
              alt="Heala word mark"
              className="cursor-pointer w-[132px] h-[30px]"
            />
          ) : (
            <HealaLogo width={30} height={30} />
          )}
        </div>
      </div>
      <ConfirmSignoutModal open={confirmModal} setOpen={setConfirmModal} />
    </aside>
  );
};
