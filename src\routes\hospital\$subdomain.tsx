import { createFileRoute, Outlet } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Head } from '@/components/head'
import { SpinnerLoader } from '@/components/spinner-loader'
import { usePartnerData } from '@/features/consultation/hooks/use-partner-data'
import { setPrimaryColor, formulateAndUpdateManifest } from '@/lib/utils'
import { useEffect } from 'react'
import { API_KEY_LS_KEY } from '@/lib/constants'

export const Route = createFileRoute('/hospital/$subdomain')({
  component: RouteComponent,
})

// Ensure we can load data for this subdomain / partner before loading any of the other sub-routes
function RouteComponent() {
  const { subdomain } = Route.useParams()
  const { loading, error, partnerData } = usePartnerData({ subdomain })

  const { widgetColor, widgetLogo, apiKey } = partnerData || {}

  useEffect(() => {
    setPrimaryColor(widgetColor || '')
    formulateAndUpdateManifest(subdomain, widgetLogo || '')
    sessionStorage.setItem(API_KEY_LS_KEY, apiKey || '')
  }, [widgetColor, widgetLogo, subdomain, apiKey])

  if (loading) return <LoadingView />

  // show error view if there's an error loading partner data
  if (error) return <PartnerErrorView errorMessage={error.message} />

  return (
    <>
      <Head title={subdomain || undefined} withoutSuffix />
      <Outlet />
    </>
  )
}

function LoadingView() {
  return (
    <div className="flex items-center justify-center h-screen">
      <SpinnerLoader />
    </div>
  )
}

type PartnerErrorView = {
  errorMessage: string
}

function PartnerErrorView(props: PartnerErrorView) {
  const { errorMessage } = props

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="rounded-lg bg-white border border-neutral-100 p-6 lg:p-12">
        <div className="text-center flex flex-col justify-center items-center">
          {
            <>
              <p className="text-lg font-medium">
                Couldn't load data for hospital!
              </p>
              <p className="text-tertiary mt-2 rounded-md p-2 border border-neutral-100 bg-primary-50 text-sm">
                {errorMessage}
              </p>
              <p className="text-tertiary mt-2">Check the link</p>
              <p className="text-tertiary mt-2 text-xs">or</p>
              <Button
                className="mt-4 w-[200px]"
                onClick={() => window.location.reload()}
              >
                Try again
              </Button>
            </>
          }
        </div>
      </div>
    </div>
  )
}
