import { hospitalSteps, steps } from "@/features/hmo-consultation/utils";

export type GetAvailableDoctorsByDateResponse = {
  message: string;
  statusCode: number;
  data: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    gender: string;
    picture: string;
    idCard: string;
    cadre: string;
    rating: number;
  }>;
};

export type HospitalDoctor = GetAvailableDoctorsByDateResponse["data"][number];

export type PlansResponse = {
  message: string;
  statusCode: number;
  data: Array<{
    _id: string;
    type: string;
    name: string;
    amount: number;
    description: string;
    provider: {
      _id: string;
      email: string | null;
      phone: string | null;
      address: string | null;
      planId: string | null;
      name: string;
      icon: string;
      iconAlt: string;
      userTypeId: string;
      profileUrl: string;
      createdAt: string;
      updatedAt: string;
    };
    allowedFeatures?: {
      consultation: string;
    };
  }>;
};

export type FormStep = keyof typeof steps | keyof typeof hospitalSteps;
