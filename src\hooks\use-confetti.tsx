import { confetti } from "@tsparticles/confetti";
import { useCallback, useEffect, useRef, useState } from "react";

interface ConfettiProps {
  duration?: number;
  colors?: string[];
  shapes?: string[];
}

export const useConfetti = (options?: ConfettiProps) => {
  const {
    duration = 150000,
    colors = ["#00ff00", "#ff0000", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"],
    shapes = ["circle"],
  } = options || {};
  const [isRunning, setIsRunning] = useState(false);
  const animationEndRef = useRef<number>(0);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  const trigger = useCallback(() => {
    setIsRunning(true);
    animationEndRef.current = Date.now() + duration;
  }, [duration]);

  const randomInRange = (min: number, max: number) =>
    Math.random() * (max - min) + min;

  useEffect(() => {
    if (!isRunning || !canvasRef.current) return;

    let animationFrameId: number;
    let skew = 1;

    const context = canvasRef.current.getContext("2d");
    if (!context) return;

    const drawConfetti = () => {
      const timeLeft = animationEndRef.current - Date.now();

      if (timeLeft <= 0) {
        setIsRunning(false);
        return;
      }

      const ticks = Math.max(200, 500 * (timeLeft / duration));
      skew = Math.max(0.8, skew - 0.001);

      confetti({
        particleCount: 1,
        startVelocity: 0,
        ticks: ticks,
        origin: {
          x: Math.random(),
          y: Math.random() * skew - 0.2,
        },
        shapes: shapes,
        gravity: randomInRange(0.4, 0.6),
        scalar: randomInRange(0.4, 1),
        drift: randomInRange(-0.4, 0.4),
      });

      animationFrameId = requestAnimationFrame(drawConfetti);
    };

    drawConfetti();

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isRunning, duration, colors, shapes]);

  const ConfettiComponent = useCallback(() => {
    if (!isRunning) return null;
    return <canvas ref={canvasRef} className="confetti-canvas" />;
  }, [isRunning]);

  return { trigger, ConfettiComponent };
};
