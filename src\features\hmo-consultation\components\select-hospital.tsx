import { <PERSON><PERSON><PERSON>oa<PERSON> } from "@/components/spinner-loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { BackButton } from "@/features/hmo-consultation/components/back-button";
import { HospitalCard } from "@/features/hmo-consultation/components/hospital-card";
import { NoDataView } from "@/features/hmo-consultation/components/no-data-view";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { hospitalSteps } from "@/features/hmo-consultation/utils";
import { gql } from "@/graphql/generated";
import { useLazyQuery } from "@apollo/client";
import { useEffect, useState } from "react";


export function SelectHospital() {
  const setFormStep = useHMOStore(s => s.setFormStep)
  const setStoreValues = useHMOStore(s => s.setValues)
  const selectedHospital = useHMOStore(s => s.selectedHospital)

  const [value, setValue] = useState("");

  const [fetchHospitals, { data, error, loading }] = useLazyQuery(getHospitals, {
    variables: {
      hospitalUserTypeId: '61ed2354e6091400135e3d94'
    }
  })
  const hospitals = data?.getUserTypeProviders?.provider || []

  useEffect(() => {
    fetchHospitals()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div>
      <BackButton step={hospitalSteps.INTRO_FORM} />
      <div>
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader className="w-4" />
          </div>
        ) : (error) ? (
          <NoDataView
            title="Error"
            info={error?.message}
            buttonText="Reload"
            onReload={() => {
              fetchHospitals();
            }}
            reloading={loading}
          />
        ) : hospitals.length > 0 ? (
          <div>
            <div>
              <h4 className="text-xl">Hospitals</h4>
              <p className="text-[#666] text-xs">Select a Hospital to Continue</p>
            </div>

            <div className="mt-7 flex items-center space-x-4">
              <Input
                disabled={loading}
                placeholder="Search for hospital"
                value={value}
                onChange={(e) => {
                  const newValue = e.target.value;
                  if (newValue === "") fetchHospitals();
                  setValue(newValue);
                }}
                className="h-12"
              />
              <Button
                loading={loading}
                onClick={() => fetchHospitals({ variables: { name: value } })}
              >
                Search
              </Button>
            </div>

            <div className="space-y-7 mt-7">
              {hospitals.map((hospital, idx) => {
                const selectedHospitalId = selectedHospital?._id;
                const isActive = selectedHospitalId === hospital?._id;

                return (
                  <HospitalCard
                    key={`${idx}-${hospital?._id}`}
                    active={isActive}
                    setActive={() => {
                      setStoreValues({ selectedHospital: hospital })
                      setFormStep(hospitalSteps.SELECT_HOSPITAL_DOCTOR)
                    }}
                    hospital={hospital}
                  />
                );
              })}
            </div>
          </div>
        ) : (
          // todo: fix flash of empty state
          <NoDataView
            title="No Hospitals"
            info=""
            buttonText="Reload"
            onReload={() => {
              fetchHospitals();
            }}
            reloading={loading}
          />
        )}
      </div>
    </div>
  )
}

const getHospitals = gql(`
  query getUserTypeProviders($hospitalUserTypeId: String, $name: String) {
    getUserTypeProviders(
      filterBy: { userTypeId: $hospitalUserTypeId, name: $name }
      page: -1
    ) {
      provider {
        _id
        name
        icon
        userCount
        doctorCount
        enrolleeCount
        partnerCount
        createdAt
        updatedAt
        userTypeId {
          name
          icon
          createdAt
          updatedAt
        }
      }
      pageInfo {
        totalDocs
        limit
        offset
        hasPrevPage
        hasNextPage
        page
        totalPages
        pagingCounter
        prevPage
        nextPage
      }
    }
  }
`);

