import { create, StateCreator } from "zustand";
import { createJSONStorage, persist, PersistOptions } from "zustand/middleware";

type HospitalUserInfoState = {
  firstName: string;
  lastName: string;
  email: string;
  gender: "male" | "female";
  phoneNumber: string;
};

type HospitalUserInfoActions = {
  hasUserInfo: () => boolean;
  setUserInfo: (info: Required<HospitalUserInfoState>) => void;
};

type HospitalUserInfoStore = HospitalUserInfoActions & HospitalUserInfoState;

type PersistState = (
  config: StateCreator<HospitalUserInfoStore>,
  options: PersistOptions<HospitalUserInfoStore>,
) => StateCreator<HospitalUserInfoStore>;

export const useHospitalUserInfoStore = create<HospitalUserInfoStore>(
  (persist as PersistState)(
    (set, get) => {
      return {
        hasUserInfo: () => {
          const { firstName, gender, lastName, phoneNumber, email } =
            get() || {};

          return (
            !!firstName && !!lastName && !!gender && !!phoneNumber && !!email
          );
        },
        setUserInfo: (info) => set({ ...info }),
      } as HospitalUserInfoStore;
    },
    {
      name: "hospital-user-info",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
