import { DoctorStarRating } from "@/components/doctor-star-rating";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { Consultation } from "@/features/hmo-consultation/types";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { cn, isValidDate } from "@/lib/utils";
import { formatDistance } from "date-fns";

type PrevConsultationCardProps = {
  consultationInfo: Consultation;
};

export function PrevConsultationCard(props: PrevConsultationCardProps) {
  const { consultationInfo } = props;
  const appState = useHMOStore();
  const color = useWidgetColor();
  const doctor = consultationInfo?.doctorData;
  let doctorName = doctor?.firstName || doctor?.lastName;
  doctorName = doctorName ? `Dr. ${doctorName}` : "No name";
  const specialization = doctor?.specialization || "No specialization";
  const rating = doctor?.rating;
  const doctorImgUrl = doctor?.picture || "";
  const symptomsArr = (consultationInfo?.symptoms || []).map(
    (symptomsObj) => symptomsObj?.name,
  );
  const symptoms =
    symptomsArr?.length > 0 ? symptomsArr?.join(", ") : "No symptoms";
  const timeDistance = isValidDate(new Date(consultationInfo.updatedAt))
    ? formatDistance(new Date(consultationInfo?.updatedAt), Date.now())
    : "";

  const isSelected =
    appState.selectedPrevConsultation?._id === consultationInfo._id;
  function setSelected() {
    appState.setValues({
      selectedPrevConsultation: consultationInfo,
    });
  }

  return (
    <div
      style={isSelected ? { borderColor: color } : {}}
      onClick={setSelected}
      className={cn(
        "flex border hover:border-[#808080] rounded-lg space-x-7 p-4 cursor-pointer text-[#666]",
        { "border-2": isSelected },
      )}
    >
      <div className="flex items-center space-x-2">
        <div className=" h-12 w-12 bg-gray-200 rounded-full">
          <img
            src={doctorImgUrl}
            alt={`${doctorName}`}
            className="h-12 w-12 rounded-full"
          />
        </div>
        <div className="space-y-1">
          <p className="font-medium leading-tight">{doctorName}</p>
          <p className="text-sm">{specialization}</p>
          {rating ? (
            <DoctorStarRating rating={doctor.rating} />
          ) : (
            <p>No Rating Yet!</p>
          )}
        </div>
      </div>
      <div className="space-y-1">
        <p className="flex items-center space-x-1 text-sm">
          <span className="font-medium text-base">
            {symptomsArr?.length > 1 ? "Symptoms" : "Symptom"}:{" "}
          </span>
          <span className="text-sm">{symptoms}</span>
        </p>
        <p className="m-0 text-xs">{timeDistance} ago.</p>
      </div>
    </div>
  );
}
