import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { useFormContext } from "react-hook-form";


export function ConsultationType() {
  const form = useFormContext<FormSchemaType>();

  return (
    <>
      <FormField
        control={form.control}
        name="isFollowUp"
        render={({ field }) => (
          <FormItem className="">
            <FormLabel>Consultation Type</FormLabel>
            <FormDescription>Please, select your preferred type.</FormDescription>
            <FormControl className="mt-2">
              <RadioGroup
                onValueChange={(value) => value === "true" ? field.onChange(true) : field.onChange(false)}
                value={field.value === true ? "true" : field.value === false ? "false" : undefined}
                className="flex flex-col space-y-1"
              >
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value={"false"} />
                  </FormControl>
                  <FormLabel className="font-normal">
                    New consultation
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value={"true"} />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Follow-up consultation
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}