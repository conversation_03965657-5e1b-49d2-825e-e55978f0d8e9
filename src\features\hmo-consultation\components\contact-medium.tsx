import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function ContactMedium() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="contactMedium"
      render={({ field }) => (
        <FormItem>
          <FormLabel required>How would you like to contact your Doctor?</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select contact medium" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="chat">Chat</SelectItem>
              <SelectItem value="voice">Voice</SelectItem>
              <SelectItem value="video">Video</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}