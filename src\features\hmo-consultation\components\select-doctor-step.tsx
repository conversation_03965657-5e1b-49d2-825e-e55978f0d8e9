import { Spinner<PERSON>oader } from "@/components/spinner-loader"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BackButton } from "@/features/hmo-consultation/components/back-button"
import { NoDataView } from "@/features/hmo-consultation/components/no-data-view"
import { PrevConsultationCard } from "@/features/hmo-consultation/components/prev-consultation-card"
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { Consultation } from "@/features/hmo-consultation/types"
import { steps, updateFollowUpForm } from "@/features/hmo-consultation/utils"
import { restApi } from "@/lib/rest-api-client"
import { normalizeEmail } from "@/lib/utils"
import { useQuery } from "@tanstack/react-query"
import { useFormContext } from "react-hook-form"


export function SelectDoctor() {
  const appState = useHMOStore()
  const { providerId } = appState.partnerInfo || {}
  const { email } = appState.userData || {}
  const selectedPrevConsultation = appState.selectedPrevConsultation

  const {
    data: prevConsultationsData,
    isLoading,
    error: prevConsultationsError,
  } = useQuery({
    queryKey: ["prevConsultations", providerId, email],
    queryFn: async () =>
      await getConsultationInfo(providerId!, normalizeEmail(email)),
    enabled: !!providerId && !!email,
  });
  const prevConsultations = prevConsultationsData || [];

  const { setValue } = useFormContext<FormSchemaType>()


  if (isLoading) {
    return (
      <div className="flex flex-col items-center py-3 gap-1">
        <SpinnerLoader className="w-7 h-7" />
        <p className="text-[#666] text-xs">Loading previous consultations...</p>
      </div>
    )
  }

  return (
    <div>
      <BackButton step={steps.INTRO_FORM} />
      {prevConsultations.length < 1 ? (
        <EmptyView />
      ) : prevConsultationsError ? (
        <p>{`Error: ${prevConsultationsError?.message}`}</p>
      ) : (
        <div>
          <h2 className="text-xl">Previous Consultation(s)</h2>
          <p className="text-xs text-[#666]">Select a consultation and click "Continue"</p>

          <div className="space-y-5 mb-5 mt-5">
            {prevConsultations.map(c => {
              return (
                <PrevConsultationCard
                  consultationInfo={c}
                  key={c._id}
                />
              )
            })}

            <Button
              className="w-full"
              disabled={!selectedPrevConsultation}
              onClick={() => {
                if (!selectedPrevConsultation) return

                updateFollowUpForm(setValue, selectedPrevConsultation)
                appState.setFormStep(steps.CREATE)
              }}
            >Continue</Button>
          </div>
        </div>
      )}
    </div>
  )
}

const getConsultationInfo = async (providerId: string, email: string): Promise<Consultation[]> => {
  return restApi.get(
    `consultations?page=1&first=5&orderBy=-createdAt&providerId=${providerId}&email=${email}&status=completed`
  ).then(res => res.data.data);
};

function EmptyView() {
  const appState = useHMOStore()
  const { setValue } = useFormContext<FormSchemaType>()

  return (
    <NoDataView
      title="No Previous Doctors"
      info="You can create a new consultation by clicking the button below"
      buttonText="Start New Consultation"
      onReload={() => {
        setValue("isFollowUp", false)
        appState.setFormStep(steps.CREATE);
      }}
    />
  )
}