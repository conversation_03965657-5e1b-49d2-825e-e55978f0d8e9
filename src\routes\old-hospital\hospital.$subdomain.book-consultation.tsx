import { Doctor<PERSON>tarR<PERSON> } from '@/components/doctor-star-rating'
import { SpinnerLoader } from '@/components/spinner-loader'
import { Button } from '@/components/ui/button'
import { usePartnerData } from '@/features/consultation/hooks/use-partner-data'
import { usePartnerPlansData } from '@/features/consultation/hooks/use-partner-plans-data'
import { ConsultationDrawer } from '@/features/hospital-consultation/components/consultation-drawer'
import { useHospitalUserInfoStore } from '@/features/hospital-consultation/hooks/use-hospital-user-info-store'
import {
  GetAvailableDoctorsByDateResponse,
  HospitalDoctor,
} from '@/features/consultation/types'
import { restApi } from '@/lib/rest-api-client'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { format } from 'date-fns'
import { useState } from 'react'
import { ConsultationDatePicker } from '@/components/consultation-date-input'
import { NoDataView } from '@/features/hmo-consultation/components/no-data-view'
import { useWidgetColor } from '@/hooks/useWidgetColor'
import { ErrorSvgIllustration } from '@/components/icons/error-svg-illustration'
import { formatName } from '@/lib/utils'

export const Route = createFileRoute(
  '/old-hospital/hospital/$subdomain/book-consultation',
)({
  component: RouteComponent,
})

function RouteComponent() {
  const hasUserInfo = useHospitalUserInfoStore((s) => s.hasUserInfo)
  const navigate = useNavigate({
    from: '/hospital/$subdomain/book-consultation',
  })

  const { subdomain } = Route.useParams()
  const { partnerData } = usePartnerData({ subdomain })
  const { plansData } = usePartnerPlansData({
    providerId: partnerData?.providerId,
  })

  const [selectedDate, setSelectedDate] = useState(new Date())

  const [selectedDoctor, setSelectedDoctor] = useState<HospitalDoctor>()

  const [isConsultationDrawerOpen, setIsConsultationDrawerOpen] =
    useState(false)

  if (!hasUserInfo()) {
    navigate({ to: '/hospital/$subdomain/start' })
  }

  const {
    data,
    isLoading: loadingDoctors,
    error,
  } = useQuery({
    queryFn: ({ queryKey }) =>
      getAvailableDoctorsByDate(queryKey[1], partnerData?.providerId),
    queryKey: ['getAvailableDoctorsByDate', selectedDate] as const,
  })
  const availableDoctors = data?.data.data

  return (
    <div className="px-4 pt-4 sm:px-8 pb:10 lg:pb-20">
      <h1 className="mt-10 lg:mt-20 text-[25px] lg:text-[40px] font-semibold text-center">
        Select a Date to Schedule an Appointment with a Doctor
      </h1>

      <div className="mt-6 md:mt-10 max-w-[32rem] mx-auto">
        <ConsultationDatePicker
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />
      </div>

      <h2 className="mt-16 text-center font-medium text-xl lg:text-2xl">
        Available Doctors
      </h2>

      <div className="flex justify-center gap-4 mt-8 flex-wrap">
        <AvailableDoctors
          data={availableDoctors}
          error={!!error}
          loading={loadingDoctors}
          onDoctorSelect={(doctor) => {
            setSelectedDoctor(doctor)
            setIsConsultationDrawerOpen(true)
          }}
        />
      </div>

      {selectedDoctor && (
        <ConsultationDrawer
          isOpen={isConsultationDrawerOpen}
          doctor={selectedDoctor}
          onOpenChange={setIsConsultationDrawerOpen}
          selectedDate={selectedDate}
          planAmount={plansData?.[0].amount}
          providerId={partnerData?.providerId || undefined}
          key={selectedDoctor?._id}
        />
      )}
    </div>
  )
}

function getAvailableDoctorsByDate(
  date: Date,
  providerId: string | undefined | null,
) {
  return restApi.get<GetAvailableDoctorsByDateResponse>(
    `doctors?date=${format(date, 'yyyy-MM-dd')}&filterBy[providerId]=${providerId}`,
  )
}

function EmptyStateView() {
  return (
    <NoDataView
      title="No available doctor on this day"
      info="Kindly select another date"
    />
  )
}

type AvailableDoctorsProps = {
  data: GetAvailableDoctorsByDateResponse['data'] | undefined
  loading: boolean
  error: boolean
  onDoctorSelect: (doctor: HospitalDoctor) => void
}

function AvailableDoctors(props: AvailableDoctorsProps) {
  const color = useWidgetColor()
  const { data, loading, error, onDoctorSelect } = props

  if (loading) {
    return (
      <div className="py-20 flex flex-col items-center">
        <SpinnerLoader />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center">
        <ErrorSvgIllustration color={color || undefined} />
        <p className="mb-4 -mt-6">Error getting doctors</p>
        <Button className="w-[12rem]" onClick={() => window.location.reload()}>
          Refresh
        </Button>
      </div>
    )
  }

  if (!data?.length) {
    return <EmptyStateView />
  }

  return (
    <>
      {data?.map((doctor, idx) => {
        const doctorName = formatName(doctor?.firstName, doctor?.lastName)
        return (
          <a
            key={idx}
            className="w-full max-w-[361px] border border-gray-[#EDEDED] rounded-lg flex px-6 py-7 gap-2 cursor-pointer"
            title="Consult with this doctor"
            onClick={() => onDoctorSelect(doctor)}
          >
            <img
              src={doctor?.idCard}
              className="rounded-full h-[65px] w-[65px] object-cover"
              alt={`${doctorName} image`}
            />
            <div>
              <p className="font-medium">{`${doctorName}`}</p>
              <p className="font-normal">{`${doctor?.cadre || 'No Cadre'}`}</p>
              {doctor.rating ? (
                <div className="flex items-center gap-1">
                  <DoctorStarRating rating={doctor?.rating || 0} />
                  <p className="text-neutral-300 text-sm">
                    {(doctor?.rating || 0)?.toFixed?.(1)}
                  </p>
                </div>
              ) : null}
            </div>
          </a>
        )
      })}
    </>
  )
}
