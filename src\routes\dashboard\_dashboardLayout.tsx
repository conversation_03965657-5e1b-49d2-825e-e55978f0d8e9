import { gql } from "@/graphql/generated";
import { useQuery } from "@apollo/client";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import { profileFactory } from "@/lib/factories/profile-factory";
import { DashboardLayout } from "@/features/user-dashboard/DashboardLayout";
import { LoadingView } from "@/features/doctor-consultation/components/loading-view";
import {
  selectDashboardData,
  selectSetDashboardData,
  useDashboardStore,
} from "@/store/dashboardStore";
import { NoData } from "@/components/EmptyStates";

export const Route = createFileRoute("/dashboard/_dashboardLayout")({
  component: RouteComponent,
});

const PROFILE_QUERY = gql(`
  query findProfiles ($healaId: String!) {
    profiles(search: $healaId) {
      data {
        _id
        firstName
        lastName
        height
        weight
        bloodGroup
        genotype
        gender
        phoneNumber
        providerId {
          _id
        }
        plan
        status
        consultations
        createdAt
        image
        rating
        pastIllness {
          id {
            _id
            name
          }
        }
        accountId {
          _id
          email
        }
      }
    }
  }
`);

function RouteComponent() {
  const storeData = useDashboardStore(selectDashboardData);
  const setProfile = useDashboardStore(selectSetDashboardData);
  const healaId = storeData?.healaId || "";
  const { loading, error } = useQuery(PROFILE_QUERY, {
    variables: { healaId },
    onCompleted(data) {
      const profile = data?.profiles?.data?.[0] || {};
      if (profile) {
        setProfile({ profile: profileFactory(profile) });
      }
    },
  });

  if (loading) return <LoadingView />;
  if (error)
    return (
      <div className="w-screen h-screen flex justify-center items-center">
        <NoData
          isError={true}
          text="An Error has occurred"
          info={error.message}
        />
      </div>
    );

  return (
    <DashboardLayout>
      <Outlet />
    </DashboardLayout>
  );
}
