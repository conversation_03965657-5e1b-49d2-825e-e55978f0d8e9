import { FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

type ReadOnlyFieldProps = {
  label: string;
  value: string;
  className?: string
  name: string;
  placeholder?: string
  required?: boolean
}

export function ReadOnlyField(props: ReadOnlyFieldProps) {
  const { label, value, className, name, placeholder, required } = props

  return (
    <div className={cn('', className)}>
      <FormLabel>{label}</FormLabel>
      <Input
        disabled
        placeholder={placeholder}
        value={value}
        required={required}
        className="mt-2"
        name={name}
      />
    </div>
  )
}