import { BookingPreferencesStep } from "@/features/doctor-consultation/components/booking-preferences-form-step";
import { PersonalInformationStep } from "@/features/doctor-consultation/components/personal-information-step";
import { SymptomInformationStep } from "@/features/doctor-consultation/components/symptom-information-form-step";
import { useDoctorConsultationFormSteps } from "@/features/doctor-consultation/hooks/useDoctorConsultationFormSteps";
import { ConsultationDoctor } from "@/lib/factories";

type ConsultationFormsContainer = {
  doctorProfile: ConsultationDoctor;
};

export function ConsultationFormsContainer(props: ConsultationFormsContainer) {
  const { doctorProfile } = props;
  const { currentStep } = useDoctorConsultationFormSteps();

  return (
    <div className="w-full">
      {currentStep === "symptom-information" && <SymptomInformationStep />}
      {currentStep === "booking-preferences" && (
        <BookingPreferencesStep doctorProfile={doctorProfile} />
      )}
      {currentStep === "personal-information" && (
        <PersonalInformationStep doctorProfile={doctorProfile} />
      )}
    </div>
  );
}


