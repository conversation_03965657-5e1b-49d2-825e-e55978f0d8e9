import { useWidgetColor } from "@/hooks/useWidgetColor";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

type SpinnerLoaderProps = {
  className?: string;
};

export function SpinnerLoader(props: SpinnerLoaderProps) {
  const { className } = props;
  const widgetColor = useWidgetColor();

  return (
    <Loader2
      style={{ color: widgetColor }}
      className={cn("w-8 h-8 animate-spin", className)}
    />
  );
}
