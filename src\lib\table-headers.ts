import { HeaderType } from "./typescript/types";

export const consultationTableHeader: HeaderType = [
  { name: "Created at" },
  { name: "Doctor" },
  { name: "Consultation Medium" },
  { name: "Status" },
  // { name: "" },
];

export const prescriptionsTableHeader: HeaderType = [
  { name: "Date" },
  { name: "Prescribed by" },
  { name: "No Of Drug(s)" },
  { name: "" },
];

export const prescriptionTableHeader: HeaderType = [
  { name: "Drug name" },
  { name: "Dosage" },
  { name: "Frequency" },
  { name: "Mode" },
  { name: "Instructions" },
];

export const testReferralTableHeader: HeaderType = [
  { name: "Date" },
  { name: "Recommended by" },
  { name: "No Of Test(s)" },
  { name: "" },
];
