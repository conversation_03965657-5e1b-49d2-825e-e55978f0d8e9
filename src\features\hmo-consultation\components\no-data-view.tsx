import { ErrorSvgIllustration } from "@/components/icons/error-svg-illustration";
import { Button } from "@/components/ui/button";
import { useWidgetColor } from "@/hooks/useWidgetColor";

type NoDataViewProps = {
  title: string;
  info?: string;
  buttonText?: string;
  onReload?: () => void;
  reloading?: boolean;
};

export function NoDataView(props: NoDataViewProps) {
  const { onReload, reloading, title, info, buttonText } = props;
  const widgetColor = useWidgetColor();

  return (
    <div className="flex flex-col items-center">
      <ErrorSvgIllustration color={widgetColor || undefined} />
      <h4 className="text-[#2c2c2c] text-center text-xl font-medium mb-1">
        {title}
      </h4>
      {info && <p className="text-center text-sm">{info}</p>}
      {buttonText && (
        <Button className="mt-5 w-full" onClick={onReload} loading={reloading}>
          {buttonText}
        </Button>
      )}
    </div>
  );
}
