/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_API_BASE_URL: string
  readonly VITE_APP_HEALA_PROVIDER_API_KEY: string
  readonly VITE_APP_PHARMACY_FEATURE_WHITELIST: string
  readonly VITE_APP_FOLLOWUP_CONSULTATION_FEATURE_WHITELIST: string
  readonly VITE_APP_DELIVERY_FEATURE_WHITELIST: string
  readonly VITE_APP_MODE: string
  // more env variables...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}