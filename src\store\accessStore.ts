import { create } from "zustand";

type AccessStoreType = {
  step: "email" | "otp";
  email: string;
  setStep: (step: AccessStoreType["step"]) => void;
  setEmail: (email: AccessStoreType["email"]) => void;
};

export const useAccessStore = create<AccessStoreType>((set) => ({
  step: "email",
  email: "",
  setStep: (step) => set({ step }),
  setEmail: (email) => set({ email }),
}));

export const selectAccessStep = (state: AccessStoreType) => state?.step;
export const selectSetAccessStep = (state: AccessStoreType) => state?.setStep;
export const selectAccessEmail = (state: AccessStoreType) => state?.email;
export const selectSetAccessEmail = (state: AccessStoreType) => state?.setEmail;
