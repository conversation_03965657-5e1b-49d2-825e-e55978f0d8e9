import { gql } from "@/graphql/generated";
import { useQuery } from "@apollo/client";
import { NoData } from "@/components/EmptyStates";
import { createFileRoute } from "@tanstack/react-router";
import { transformDataConsultationForConsultationInfoCard } from "@/lib/utils";
import { selectDashboardData, useDashboardStore } from "@/store/dashboardStore";
import { DashboardOverviewCard } from "@/components/cards/DashboardOverviewCard";
import {
  ConsultationInfoCard,
  ConsultationInfoCardSkeleton,
} from "@/components/cards/ConsultationInfoCard";
import { useSortedConsultations } from "@/hooks/useSortedConsultations";

export const Route = createFileRoute("/dashboard/_dashboardLayout/")({
  component: Home,
});

const GET_OVERVIEW_STATS = gql(`
  query getOverviewStats($patientId: String!) {
    getConsultations(filterBy: { patient: $patientId }) {
      pageInfo {
        totalDocs
      }
    }
    getReferrals(filterBy: { patient: $patientId }) {
      pageInfo {
        totalDocs
      }
    }
    getPrescriptions(filterBy: { patient: $patientId }) {
      pageInfo {
        totalDocs
      }
    }
  }
`);

export default function Home() {
  const userProfile = useDashboardStore(selectDashboardData)?.profile;
  const patientId = userProfile?._id;

  const { loading: loadingStats, data: consultationCountData } = useQuery(
    GET_OVERVIEW_STATS,
    {
      variables: {
        patientId,
      },
    },
  );

  const { loading: loadingPendingScheduledConsultations, sortedConsultations } =
    useSortedConsultations({
      type: "scheduled",
      pendingStatus: "pending",
      acceptedStatus: "accepted",
      first: 10,
      page: 1,
      orderBy: "-createdAt",
      patientId,
    });

  const consultationCount =
    consultationCountData?.getConsultations?.pageInfo?.totalDocs || 0;

  const prescriptionCount =
    consultationCountData?.getPrescriptions?.pageInfo?.totalDocs || 0;

  const testCount =
    consultationCountData?.getReferrals?.pageInfo?.totalDocs || 0;

  return (
    <main className="w-full flex min-h-[calc(100vh-180px)]">
      <div className="w-full">
        {/* OVERVIEW CARDS */}
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-10">
          <DashboardOverviewCard
            count={consultationCount}
            text="Consultations"
            type="consultations"
            loading={loadingStats}
          />
          <DashboardOverviewCard
            count={prescriptionCount}
            text="Prescriptions"
            type="prescriptions"
            loading={loadingStats}
          />
          <DashboardOverviewCard
            count={testCount}
            text="Tests"
            type="tests"
            loading={loadingStats}
          />
        </div>

        {/* UPCOMING CONSULTATIONS  */}
        <div className="my-10 space-y-7">
          <h3 className="text-black text-xl font-medium">
            Upcoming consultations
          </h3>
          <div className="w-full">
            {/* CONSULTATIONS CARDS */}
            {loadingPendingScheduledConsultations ? (
              <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-10">
                <ConsultationInfoCardSkeleton />
                <ConsultationInfoCardSkeleton />
                <ConsultationInfoCardSkeleton />
              </div>
            ) : sortedConsultations.length === 0 ? (
              <div className="flex justify-center">
                <NoData
                  isError={false}
                  text="No Upcoming Scheduled consultations"
                />
              </div>
            ) : (
              <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-10">
                {sortedConsultations.map((consultation) => (
                  <ConsultationInfoCard
                    key={consultation._id}
                    consultationInfo={transformDataConsultationForConsultationInfoCard(
                      consultation,
                    )}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
