import { gql } from "@/graphql/generated";
import { useQuery as useApolloQuery } from "@apollo/client";
import { useCallback, useEffect, useMemo, useState } from "react";
import MultipleSelector, { Option } from "@/components/ui/multiple-selector";

type SymptomSelectorInputProps = {
  onChange: (values: { name: string }[]) => void;
  initialValues?: { name: string }[];
};

export function SymptomSelectorInput(props: SymptomSelectorInputProps) {
  const { onChange, initialValues } = props;
  const [values, setValues] = useState<Option[]>(
    (initialValues || []).map((i) => ({ value: i.name!, label: i.name })),
  );
  const { data } = useApolloQuery(issuesQuery);

  const issues = data?.getIssues.issues;
  const issuesOptions = useMemo(
    () =>
      issues ? issues.map((i) => ({ value: i.Name!, label: i.Name! })) : [],
    [issues],
  );

  const _onChange = useCallback(
    (values: Option[]) => {
      const transformedValues = values.map((v) => ({ name: v.value }));
      onChange(transformedValues);
    },
    [onChange],
  );

  useEffect(() => {
    _onChange(values);
  }, [values, _onChange]);

  return (
    <MultipleSelector
      options={issuesOptions}
      value={values}
      onChange={setValues}
      placeholder="Describe your symptom or select from the menu"
      triggerSearchOnFocus={false}
      onSearchSync={(value) => {
        // allow to capture/expose custom option(i.e. user typed-text not in options) even if not yet created(i.e. using enter)
        const valuesCopy = [...values];
        valuesCopy[valuesCopy.length] = { label: value, value };

        _onChange(valuesCopy);

        return valuesCopy;
      }}
      creatable
      hideClearAllButton
      // todo: hide select dropdown if no match or field empty
    />
  );
}

const issuesQuery = gql(`
  query getIssues {
    getIssues {
      issues {
        ID
        Name
      }
    }
  }
`);
