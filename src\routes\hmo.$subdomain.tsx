import { FullScreenSpinnerLoader } from "@/components/full-screen-spinner-loader";
import { Head } from "@/components/head";
import { Button } from "@/components/ui/button";
import { usePartnerData } from "@/features/consultation/hooks/use-partner-data";
import { usePartnerPlansData } from "@/features/consultation/hooks/use-partner-plans-data";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { API_KEY_LS_KEY } from "@/lib/constants";
import {
  setPrimaryColor,
  formulateAndUpdateManifest,
  getProviderFeatures,
} from "@/lib/utils";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import { useEffect } from "react";

export const Route = createFileRoute("/hmo/$subdomain")({
  component: RouteComponent,
});

// Ensure we can load data for this subdomain / partner before loading any of the other sub-routes
function RouteComponent() {
  const { subdomain } = Route.useParams();
  const {
    loading: partnerDataLoading,
    error: partnerDataError,
    partnerData,
  } = usePartnerData({ subdomain });
  const { plansData, loading: plansDataLoading } = usePartnerPlansData({
    providerId: partnerData?.providerId,
  });

  const loading = partnerDataLoading || plansDataLoading;

  const { widgetColor, widgetLogo } = partnerData || {};

  useEffect(() => {
    setPrimaryColor(widgetColor || "");
    formulateAndUpdateManifest(subdomain, widgetLogo || "");
  }, [widgetColor, widgetLogo, subdomain]);

  const { setValues: setStoreValues } = useHMOStore();

  useEffect(() => {
    setStoreValues({
      plans: plansData,
      partnerInfo: {
        ...(partnerData || {}),
      },
      providerFeatures: getProviderFeatures(
        partnerData?.providerId || undefined,
      ),
    });
    sessionStorage.setItem(API_KEY_LS_KEY, partnerData?.apiKey || "");
  }, [partnerData, plansData, setStoreValues]);

  if (loading) return <FullScreenSpinnerLoader />;

  // show error view if there's an error loading partner data
  if (partnerDataError)
    return <PartnerErrorView errorMessage={partnerDataError.message} />;

  return (
    <>
      <Head title={subdomain || undefined} withoutSuffix />
      <Outlet />
    </>
  );
}

type PartnerErrorView = {
  errorMessage: string;
};

function PartnerErrorView(props: PartnerErrorView) {
  const { errorMessage } = props;

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="rounded-lg bg-white border border-neutral-100 p-6 lg:p-12">
        <div className="text-center flex flex-col justify-center items-center">
          {
            <>
              <p className="text-lg font-medium">Couldn't load data!</p>
              <p className="text-tertiary mt-2 rounded-md p-2 border border-neutral-100 bg-primary-50 text-sm">
                {errorMessage}
              </p>
              <p className="text-tertiary mt-2">Check the link</p>
              <p className="text-tertiary mt-2 text-xs">or</p>
              <Button
                className="mt-4 w-[200px]"
                onClick={() => window.location.reload()}
              >
                Try again
              </Button>
            </>
          }
        </div>
      </div>
    </div>
  );
}
