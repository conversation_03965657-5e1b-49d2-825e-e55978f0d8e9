import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format, startOfToday } from "date-fns";
import { CalendarIcon } from "lucide-react";

type ConsultationDatePickerProps = {
  onDateChange: (value: Date) => void;
  selectedDate: Date;
};

export function ConsultationDatePicker(props: ConsultationDatePickerProps) {
  const { onDateChange, selectedDate } = props;

  return (
    <div className="flex flex-col">
      <div className="mb-2 text-left">Consultation Date</div>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn("pl-3 text-left font-normal")}
          >
            {format(selectedDate, "PPP")}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(v) => {
              onDateChange(v!);
            }}
            disabled={(date) => date < startOfToday()}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}