import React from "react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { AvatarFallback } from "@radix-ui/react-avatar";
import { ChevronRight } from "lucide-react";
import { GetUserTypeProvidersQuery } from "@/graphql/generated/graphql";
import { useWidgetColor } from "@/hooks/useWidgetColor";

type HospitalCardProps = {
  active: boolean;
  setActive: () => void;
  hospital: NonNullable<
    GetUserTypeProvidersQuery["getUserTypeProviders"]["provider"]
  >[0];
};

export const HospitalCard = (props: HospitalCardProps) => {
  const { active = false, setActive, hospital } = props;

  const color = useWidgetColor();

  return (
    <div
      onClick={setActive}
      style={active ? { borderColor: color } : {}}
      className={cn(
        "flex items-center justify-between bg-white hover:bg-slate-50 border rounded-lg space-x-7 px-5 py-7 cursor-pointer",
        { "border-2": active },
      )}
    >
      <div className="flex items-center space-x-5">
        <Avatar className="border-2">
          <AvatarImage src={hospital?.icon || ""} />
          <AvatarFallback>
            {(hospital?.name?.split(" ") || [])
              .map((name: string) => name.substring(0, 1))
              .join("")}
          </AvatarFallback>
        </Avatar>
        <p className="!text-base">{hospital?.name || "No name"}</p>
      </div>
      <ChevronRight />
    </div>
  );
};
