import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { restApi } from '@/lib/rest-api-client'
import axios from 'axios'
import { useHospitalUserInfoStore } from '@/features/hospital-consultation/hooks/use-hospital-user-info-store'
import { useToast } from '@/hooks/use-toast'
import { isValidPhoneNumber } from 'react-phone-number-input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useWidgetColor } from '@/hooks/useWidgetColor'

export const Route = createFileRoute('/old-hospital/hospital/$subdomain/start')(
  {
    component: RouteComponent,
  },
)

const steps = ['enter-email-step', 'signup-step'] as const
type Step = (typeof steps)[number]

function RouteComponent() {
  const [step, setStep] = useState<Step>(steps[0])

  return (
    <Layout>
      {step === 'enter-email-step' && <EnterEmailStep setStep={setStep} />}
      {step === 'signup-step' && <SignupStep />}
    </Layout>
  )
}

type EnterEmailStepProps = {
  setStep: (step: Step) => void
}

function EnterEmailStep(props: EnterEmailStepProps) {
  const { setStep } = props
  const form = useForm({
    resolver: zodResolver(z.object({ email: z.string().email() })),
    defaultValues: {
      email: '',
    },
  })

  const setPersonalInfo = useHospitalUserInfoStore((s) => s.setUserInfo)

  const { toast } = useToast()

  const navigate = useNavigate({ from: '/hospital/$subdomain/start' })

  const onSubmit = async (values: { email: string }) => {
    const checkEmailResponse = await checkEmail(values.email)

    if (checkEmailResponse.success) {
      setPersonalInfo({
        ...checkEmailResponse.userProfile!,
        email: values.email,
      })
      navigate({ to: '/hospital/$subdomain/book-consultation' })
      return
    } else {
      if (checkEmailResponse.noProfile) {
        setStep('signup-step')
      } else {
        toast({
          title: 'Error validating email',
          description: checkEmailResponse.errorMessage,
          variant: 'destructive',
        })
        return
      }
    }
  }

  return (
    <div className="flex-1 flex flex-col justify-center">
      <h1 className="text-[40px] font-semibold">Get Started</h1>
      <p className="text-sm">
        Welcome to the consultation portal. Let's get you paired with a doctor
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="mt-8">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input type="email" {...field} placeholder="Email address" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-8 lg:pt-6">
            <Button
              className="w-full"
              type="submit"
              loading={form.formState.isSubmitting}
            >
              Next
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

const SignupStepFormSchema = z.object({
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  email: z.string().email(),
  phoneNumber: z.string().refine((text) => isValidPhoneNumber(text, 'NG'), {
    message: 'Invalid phone number',
  }),
  gender: z.enum(['male', 'female']),
  isAgreedToTerms: z
    .boolean()
    .default(false)
    .refine((value) => value, {
      message: 'You must agree to the terms and conditions.',
    }),
})

function SignupStep() {
  const color = useWidgetColor()
  const email = useHospitalUserInfoStore((s) => s.email)
  const setUserInfo = useHospitalUserInfoStore((s) => s.setUserInfo)

  const form = useForm<z.infer<typeof SignupStepFormSchema>>({
    resolver: zodResolver(SignupStepFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email,
      phoneNumber: '',
      gender: undefined,
    },
  })

  const navigate = useNavigate({ from: '/hospital/$subdomain/start' })

  const onSubmit = async (values: z.infer<typeof SignupStepFormSchema>) => {
    setUserInfo(values)
    navigate({ to: '/hospital/$subdomain/book-consultation' })
  }

  return (
    <div className="flex-1 flex flex-col justify-center pt-10 lg:pt-20">
      <h1 className="text-[40px] font-semibold">Sign up</h1>
      <p className="text-sm">Welcome to Heala. Let's get you signed up</p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="mt-8 space-y-5">
          <div className="flex flex-col sm:flex-row gap-5">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input type="email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <PhoneInput {...field} defaultCountry="NG" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gender</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isAgreedToTerms"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-start gap-2 pt-2">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="w-4 h-4"
                    />
                  </FormControl>
                  <FormLabel className="-mt-0.5">
                    By signing up, you agree to{' '}
                    <a
                      href="https://heala.ng/terms/"
                      target="_blank"
                      style={{ color }}
                      className="text-center text-primary text-sm"
                    >
                      Heala's Terms & Conditions
                    </a>
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-8 lg:pt-6">
            <Button
              className="w-full"
              type="submit"
              loading={form.formState.isSubmitting}
            >
              Next
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

function Layout(props: React.PropsWithChildren) {
  return (
    <div className="h-screen flex">
      {/* image */}
      <div className="hidden h-full md:block flex-1">
        <img
          src="/hospital-consultation-start-image.jpeg"
          className="w-full h-full object-cover"
        />
      </div>

      <div className="px-6 lg:px-12 md:max-w-[582px] lg:w-[582px] overflow-y-scroll">
        <div className="min-h-screen mx-auto flex flex-col">
          {props.children}
          <div className="flex-shrink py-6 lg:py-10 text-center">
            <div className="flex justify-center items-center gap-2">
              <p>Powered by</p>
              <a href="https://heala.ng" target="_blank">
                <img
                  src="/heala-logo.svg"
                  height={21}
                  width={73}
                  className=" grayscale"
                  alt="Heala logo"
                />
              </a>
            </div>
            {/* <a
              href="https://heala.ng/terms/"
              target="_blank"
              className="text-center text-primary text-sm"
            >
              Heala's terms and conditions
            </a> */}
          </div>
        </div>
      </div>
    </div>
  )
}

async function checkEmail(email: string) {
  try {
    const res = await restApi.post<ValidateEmailResponse>(
      '/consultations/validate/email',
      { email },
    )

    if (res.data.statusCode === 201) {
      return {
        success: true,
        userProfile: res.data.data.data,
      }
    } else {
      throw new axios.AxiosError(undefined, undefined, undefined, {}, res)
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.data?.statusCode === 404) {
        return {
          success: false,
          noProfile: true,
        }
      } else {
        console.log('Error validating email: ', error)
        return {
          success: false,
          errorMessage:
            error.response?.data?.message || 'Something went wrong!',
        }
      }
    } else {
      console.log('Unknown error validating email: ', error)

      return {
        success: false,
        errorMessage: (error as Error).message || 'Something went wrong',
      }
    }
  }
}

type ValidateEmailResponse = {
  message: string
  statusCode: number
  data: {
    data: {
      firstName: string
      lastName: string
      gender: 'male' | 'female'
      phoneNumber: string
    }
    message: string
  }
  path: string
}
