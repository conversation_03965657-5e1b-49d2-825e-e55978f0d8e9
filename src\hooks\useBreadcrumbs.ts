import { useMemo } from "react";
import { useRouterState } from "@tanstack/react-router";

interface BreadcrumbMapping {
  path: string;
  label: string;
}

const breadcrumbMappings: BreadcrumbMapping[] = [
  { path: "dashboard", label: "Dashboard" },
  { path: "consultations", label: "Consultations" },
  { path: "prescriptions", label: "Prescriptions" },
  { path: "tests", label: "Tests" },
];

export const useBreadcrumbs = (): string => {
  const routerState = useRouterState();
  const pathname = routerState.location.pathname;

  const currentPathArray = useMemo(() => {
    const matchedBreadcrumbs = breadcrumbMappings.filter((breadcrumb) => {
      const text = pathname.replace(/^\//, "");
      return breadcrumb.path.includes(text.replace(/^dashboard\//, ""));
    });

    // To only show unique path segments, we can map based on the longest matching paths
    return matchedBreadcrumbs.sort((a, b) => a.path.length - b.path.length);
  }, [pathname]);

  return currentPathArray[0]?.label || "";
};
