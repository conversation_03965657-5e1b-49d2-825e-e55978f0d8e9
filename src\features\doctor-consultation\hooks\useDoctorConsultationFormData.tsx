import { isValidPhoneNumber } from "react-phone-number-input";
import { z } from "zod";
import { create, StateCreator } from "zustand";
import { createJSONStorage, persist, PersistOptions } from "zustand/middleware";

type DoctorConsultationFormStoreState = Partial<{
  symptoms: SymptomInformationStepFormSchemaType["symptoms"];
  discomfortLevel: SymptomInformationStepFormSchemaType["discomfortLevel"];
  firstNotice: SymptomInformationStepFormSchemaType["firstNotice"];
  description?: SymptomInformationStepFormSchemaType["description"];
  date: BookingPreferencesStepFormSchemaType["date"];
  time: BookingPreferencesStepFormSchemaType["time"];
  channel: BookingPreferencesStepFormSchemaType["channel"];
  email: BookingPreferencesStepFormSchemaType["email"];
  firstName: PersonalInformationStepFormSchemaType["firstName"];
  lastName: PersonalInformationStepFormSchemaType["lastName"];
  phoneNumber: PersonalInformationStepFormSchemaType["phoneNumber"];
  gender: PersonalInformationStepFormSchemaType["gender"];
}>;

type DoctorConsultationFormStoreActions = {
  setFormData: (data: Partial<DoctorConsultationFormStoreState>) => void;
  resetFormData: () => void;
};

type DoctorConsultationFormStore = DoctorConsultationFormStoreState &
  DoctorConsultationFormStoreActions;

type PersistState = (
  config: StateCreator<DoctorConsultationFormStore>,
  options: PersistOptions<DoctorConsultationFormStore>,
) => StateCreator<DoctorConsultationFormStore>;

export const useDoctorConsultationFormDataStore =
  create<DoctorConsultationFormStore>(
    (persist as PersistState)(
      (set, get) => ({
        setFormData: (data) => set(data),
        resetFormData: () => set({
          setFormData: get().setFormData,
          resetFormData: get().resetFormData
        }),
      }),
      {
        name: "doctor-consultation-form",
        storage: createJSONStorage(() => sessionStorage),
      },
    ),
  );

// Form step schemas

export const SymptomInformationStepFormSchema = z.object({
  symptoms: z
    .array(z.object({ name: z.string() }))
    .min(1, { message: "This field is required!" }),
  discomfortLevel: z.enum(["None", "Mild", "Medium", "Intense"]),
  firstNotice: z.enum(["This week", "Last week", "One month ago"]),
  description: z.string().optional(),
});

export type SymptomInformationStepFormSchemaType = z.infer<
  typeof SymptomInformationStepFormSchema
>;

export const BookingPreferencesStepFormSchema = z.object({
  date: z.date(),
  time: z.string().min(2, "Required"),
  channel: z.enum(["video", "chat", "voice"]),
  email: z.string().email(),
});

export type BookingPreferencesStepFormSchemaType = z.infer<
  typeof BookingPreferencesStepFormSchema
>;

export const PersonalInformationStepFormSchema = z.object({
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  phoneNumber: z
    .string()
    .refine((text) => isValidPhoneNumber(text, "NG"), {
      message: "Invalid phone number",
    })
    .or(z.literal("")),
  gender: z.enum(["male", "female"]),
});

export type PersonalInformationStepFormSchemaType = z.infer<
  typeof PersonalInformationStepFormSchema
>;
