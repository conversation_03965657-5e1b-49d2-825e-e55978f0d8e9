import { HomeIcon } from "@/components/icons/dashboard-icons/home";
import { ConsultationTableData, NavigationItem } from "./typescript/types";
import { ConsultationIcon } from "@/components/icons/dashboard-icons/consultationIcon";
import { PrescriptionsIcon } from "@/components/icons/dashboard-icons/prescriptionsIcon";
import { TestsIcon } from "@/components/icons/dashboard-icons/TestsIcon";
import { SignoutIcon } from "@/components/icons/dashboard-icons/signOut";

export const navigationItems: NavigationItem[] = [
  {
    name: "Home",
    icon: HomeIcon,
    path: "/dashboard",
  },
  {
    name: "Consultations",
    icon: ConsultationIcon,
    path: "/dashboard/consultations",
  },
  {
    name: "Prescriptions",
    icon: PrescriptionsIcon,
    path: "/dashboard/prescriptions",
  },
  {
    name: "Tests",
    icon: TestsIcon,
    path: "/dashboard/tests",
  },
  {
    name: "Sign out",
    icon: SignoutIcon,
    path: "/access",
  },
];

export const consultationTableTestData: ConsultationTableData[] = Array(
  10,
).fill({
  dateTime: "12/09/2024 - 9:00am",
  doctor: "<PERSON><PERSON>",
  "consultation Medium": "Audio",
  Status: "Ongoing",
});

export const defaultPageInfo = {
  totalPages: 1,
  page: 1,
  limit: 10,
};

export const healaColor = "#3E5EA9";
