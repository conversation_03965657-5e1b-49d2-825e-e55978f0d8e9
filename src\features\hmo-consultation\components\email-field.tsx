import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function EmailField() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="email"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email Address</FormLabel>
          <FormControl>
            <Input type="email" {...field} placeholder="Email address" />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}