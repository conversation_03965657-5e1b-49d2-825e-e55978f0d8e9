import { IconProps } from "@/lib/typescript/types";

export const PrescriptionsIcon: React.FC<IconProps> = ({ size, className }) => {
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_5641_57731)">
        <path d="M18.5 5H17V3.5C17 2.57174 16.6313 1.6815 15.9749 1.02513C15.3185 0.368749 14.4283 0 13.5 0H10.5C9.57174 0 8.6815 0.368749 8.02513 1.02513C7.36875 1.6815 7 2.57174 7 3.5V5H5.5C4.0418 5.00159 2.64377 5.58156 1.61267 6.61267C0.581561 7.64377 0.00158817 9.0418 0 10.5L0 18.5C0.00158817 19.9582 0.581561 21.3562 1.61267 22.3873C2.64377 23.4184 4.0418 23.9984 5.5 24H18.5C19.9582 23.9984 21.3562 23.4184 22.3873 22.3873C23.4184 21.3562 23.9984 19.9582 24 18.5V10.5C23.9984 9.0418 23.4184 7.64377 22.3873 6.61267C21.3562 5.58156 19.9582 5.00159 18.5 5ZM10 3.5C10 3.36739 10.0527 3.24021 10.1464 3.14645C10.2402 3.05268 10.3674 3 10.5 3H13.5C13.6326 3 13.7598 3.05268 13.8536 3.14645C13.9473 3.24021 14 3.36739 14 3.5V5H10V3.5ZM21 18.5C21 19.163 20.7366 19.7989 20.2678 20.2678C19.7989 20.7366 19.163 21 18.5 21H5.5C4.83696 21 4.20107 20.7366 3.73223 20.2678C3.26339 19.7989 3 19.163 3 18.5V10.5C3 9.83696 3.26339 9.20107 3.73223 8.73223C4.20107 8.26339 4.83696 8 5.5 8H18.5C19.163 8 19.7989 8.26339 20.2678 8.73223C20.7366 9.20107 21 9.83696 21 10.5V18.5ZM16.5 14.5C16.5 14.8978 16.342 15.2794 16.0607 15.5607C15.7794 15.842 15.3978 16 15 16H13.5V17.5C13.5 17.8978 13.342 18.2794 13.0607 18.5607C12.7794 18.842 12.3978 19 12 19C11.6022 19 11.2206 18.842 10.9393 18.5607C10.658 18.2794 10.5 17.8978 10.5 17.5V16H9C8.60218 16 8.22064 15.842 7.93934 15.5607C7.65804 15.2794 7.5 14.8978 7.5 14.5C7.5 14.1022 7.65804 13.7206 7.93934 13.4393C8.22064 13.158 8.60218 13 9 13H10.5V11.5C10.5 11.1022 10.658 10.7206 10.9393 10.4393C11.2206 10.158 11.6022 10 12 10C12.3978 10 12.7794 10.158 13.0607 10.4393C13.342 10.7206 13.5 11.1022 13.5 11.5V13H15C15.3978 13 15.7794 13.158 16.0607 13.4393C16.342 13.7206 16.5 14.1022 16.5 14.5Z" />
      </g>
      <defs>
        <clipPath id="clip0_5641_57731">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
