import { gql } from "@/graphql/generated";
import { useQuery } from "@apollo/client";

type UseDoctorDataProps = {
  profileId: string;
};

export function useDoctorData(props: UseDoctorDataProps) {
  const { profileId } = props;

  const { data, loading, error } = useQuery(doctorProfileQuery, {
    variables: { profileId },
  });

  const doctorProfile = data?.doctorProfile.profile;
  const isProfileNotFound = !loading && doctorProfile === null;

  return {
    doctorProfile,
    isLoading: loading,
    error,
    isProfileNotFound,
  };
}

const doctorProfileQuery = gql(`
    query doctorProfile($profileId: String!) {
      doctorProfile(data: { id: $profileId }) {
        profile {
          _id
          firstName
          lastName
          gender
          phoneNumber
          createdAt
          updatedAt
          email
          fee
          hospital
          specialization
          dob
          cadre
          picture
          providerId {
            _id
            name
          }
          status
          dociId
          rating
          accountDetails {
            accountName
            accountNumber
            bankName
          }
        }
      }
    }
  `);
