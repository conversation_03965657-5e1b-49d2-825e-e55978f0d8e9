import { Consultation<PERSON>imeField } from "@/components/consultation-time-field";
import { <PERSON><PERSON><PERSON>ly<PERSON><PERSON> } from "@/components/read-only-field";
import { SpinnerLoader } from "@/components/spinner-loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FormLabel } from "@/components/ui/form";
import { SymptomsField } from "@/features/hmo-consultation/components/symptoms-field";
// import { AdditionalInformation } from "@/features/hmo-consultation/components/additional-information";
import { AddressInput } from "@/features/hmo-consultation/components/address-input";
import { BackButton } from "@/features/hmo-consultation/components/back-button";
import { ContactMedium } from "@/features/hmo-consultation/components/contact-medium";
import { DiscomfortLevel } from "@/features/hmo-consultation/components/discomfort-level";
import { DOB } from "@/features/hmo-consultation/components/dob-field";
import { EmailField } from "@/features/hmo-consultation/components/email-field";
import { FirstNotice } from "@/features/hmo-consultation/components/first-notice";
import { Gender<PERSON>ield } from "@/features/hmo-consultation/components/gender-field";
import { PharmacyAndDeliverySelectionField } from "@/features/hmo-consultation/components/pharmacy-and-delivery-selection-field";
import { PhoneNumber } from "@/features/hmo-consultation/components/phone-number-field";
import { PrevConsultationCard } from "@/features/hmo-consultation/components/prev-consultation-card";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import {
  formatValues,
  getClosestPharmacies,
  extraValidate,
  hideInputForFollowUp,
  updateUserInfo,
  getPrevStepInCreateConsultationForm,
} from "@/features/hmo-consultation/utils";
import { getWellaHealthPharmaciesQuery } from "@/graphql/queries";
import { toast } from "@/hooks/use-toast";
import { restApi } from "@/lib/rest-api-client";
import { checkAddress, checkAndFormatAddress } from "@/lib/utils";
import { useLazyQuery as useLazyApolloQuery } from "@apollo/client";
import axios from "axios";
import { useEffect, useMemo, useState } from "react";
import { FieldErrors, useFormContext } from "react-hook-form";

export function NewConsultation() {
  const providerFeatures = useHMOStore((s) => s.providerFeatures);
  const hasPharmacyFeature = !!providerFeatures?.hasPharmacyFeature;
  const hasAddressFeature = !!providerFeatures?.hasAddressFeature;

  const {
    formState: { errors },
    handleSubmit,
    setError,
    watch,
    setValue,
  } = useFormContext<FormSchemaType>();
  const [submissionError, setSubmissionError] = useState<null | string>(null);
  const [loading, setLoading] = useState(false);

  const [getPharmacies, { data: pharmacies, loading: loadingPharmacies }] =
    useLazyApolloQuery(getWellaHealthPharmaciesQuery);
  const pharmacyArray = pharmacies?.getPharmaciesFromWellaHealth?.data;

  const address = checkAndFormatAddress(watch("address") || "");

  const isAddressComplete = useMemo(() => {
    return checkAddress(address);
  }, [address]);

  const showPharmacySelectInput = useMemo(() => {
    return isAddressComplete && pharmacyArray && pharmacyArray?.length > 0;
  }, [isAddressComplete, pharmacyArray]);

  // Set address in address field and get closet pharmacies
  useEffect(() => {
    if (isAddressComplete && hasPharmacyFeature) {
      getClosestPharmacies(getPharmacies, address);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddressComplete]);

  const isFollowUp = watch("isFollowUp");
  const selectedPrevConsultation = useHMOStore(
    (s) => s.selectedPrevConsultation,
  );
  const isHospitalConsultation = useHMOStore((s) => s.isHospitalConsultation);
  const selectedHospital = useHMOStore((s) => s.selectedHospital);
  const prevStep = getPrevStepInCreateConsultationForm(
    !!isFollowUp,
    !!isHospitalConsultation,
  );

  const selectedDoctor = useHMOStore((s) => s.selectedDoctor);
  const doctorName = `Dr. ${selectedDoctor?.firstName} ${selectedDoctor?.lastName}`;
  const selectedDate = useHMOStore((s) => s.selectedDate);

  const hmoUserObj = useHMOStore((s) => s.userData);

  const showEmailInput = !hmoUserObj?.email || Boolean(errors["email"]);
  const showPhoneNumInput =
    !hmoUserObj?.phone || Boolean(errors["phoneNumber"]);
  const showGenderInput = !hmoUserObj?.gender || Boolean(errors["gender"]);
  const showDOBInput = !hmoUserObj?.dob || Boolean(errors["dob"]);

  const showAddressInput = hasPharmacyFeature || hasAddressFeature;

  async function onSubmit(values: FormSchemaType) {
    try {
      setSubmissionError("");
      setLoading(true);
      const { isValid, error } = extraValidate({
        hasPharmacyFeature: !!hasPharmacyFeature,
        values,
        pharmacies: pharmacyArray!,
        isFollowUp: false,
      });

      if (!isValid) {
        if (error.name === "none") {
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          });
          return;
        }

        setError(
          error.name,
          { type: "manual", message: error?.message },
          { shouldFocus: true },
        );
        setSubmissionError("Missing Fields!");
        setLoading(false);
        return;
      }

      updateUserInfo(values, hmoUserObj).catch((err) => console.error(err));

      // @ts-expect-error need to type form schema to include 'time' field
      if (values.time) {
        // @ts-expect-error need to type form schema to include 'time' field
        const dateString = `${selectedDate}T${values.time}`;
        // @ts-expect-error need to type form schema to include 'time' field
        values.time = dateString;
      }

      const formattedData = formatValues(
        values,
        (pharmacyArray || []).length > 0,
        !!isHospitalConsultation,
      );
      const response = await restApi.post(
        "/consultations/create",
        formattedData,
      );

      const consultationURL = response.data.data.url;
      window.open(consultationURL, "_self");
    } catch (error) {
      setLoading(false);
      console.error(error);

      if (axios.isAxiosError(error)) {
        setSubmissionError(error.response?.data.message);
        return;
      }

      if (typeof error === "object" && error !== null && "message" in error) {
        setSubmissionError(error.message as string);
      }
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <BackButton step={prevStep} />
      <div className="space-y-3">
        {isFollowUp && selectedPrevConsultation && (
          <PrevConsultationCard consultationInfo={selectedPrevConsultation} />
        )}

        {isHospitalConsultation && !isFollowUp && (
          <ReadOnlyField
            name="hospital"
            label="Hospital"
            value={selectedHospital?.name || ""}
            required={true}
          />
        )}

        {isHospitalConsultation && !isFollowUp && (
          <ReadOnlyField
            name="doctor"
            label="Doctor"
            value={doctorName}
            required={true}
          />
        )}

        {isHospitalConsultation && !isFollowUp && (
          <div>
            <FormLabel required={true}>
              Select a time for your consultation
            </FormLabel>
            <ConsultationTimeField
              doctorProfileId={selectedDoctor?._id}
              selectedDate={new Date(selectedDate!)}
              onChange={(value) => {
                // @ts-expect-error need to type the form schema to include "time"
                setValue("time", value);
              }}
            />
          </div>
        )}

        {showGenderInput && <GenderField />}

        {showDOBInput && <DOB />}

        {showPhoneNumInput && <PhoneNumber />}

        {showEmailInput && <EmailField />}

        {/* todo: fix field disappearing after resolving error */}
        {!hideInputForFollowUp(!!isFollowUp, Boolean(errors?.symptoms)) && (
          <SymptomsField />
        )}

        {!hideInputForFollowUp(
          !!isFollowUp,
          Boolean(errors?.discomfortLevel),
        ) && <DiscomfortLevel />}

        {!hideInputForFollowUp(!!isFollowUp, Boolean(errors?.firstNotice)) && (
          <FirstNotice />
        )}

        <ContactMedium />

        {showAddressInput && <AddressInput />}

        {loadingPharmacies && <LoadingPharmacies />}

        {hasPharmacyFeature && showPharmacySelectInput && (
          <PharmacyAndDeliverySelectionField
            loadingPharmacies={loadingPharmacies}
            pharmacies={pharmacyArray}
          />
        )}

        {/* <AdditionalInformation /> */}

        <div className="btn-block">
          {errors && Object.keys(errors || {})?.length > 0 && (
            <>
              <p className="text-red-500 text-sm">
                Missing fields! Please crosscheck your form or contact support.
              </p>
              <FieldErrorsSummary errors={errors} />
            </>
          )}
          {submissionError && (
            <p className="text-red-500 text-sm mt-2">{submissionError}</p>
          )}

          <div className="mt-6">
            <Button
              formNoValidate={true}
              type="submit"
              loading={loading}
              className="w-full"
            >
              Submit
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}

type FieldErrorsSummaryProps = {
  errors: FieldErrors<FormSchemaType>;
};

const FieldErrorsSummary = (props: FieldErrorsSummaryProps) => {
  const { errors } = props;

  if (!errors) return null;

  console.error("errors: ", { errors });

  const fieldErrorsTuple = (Object.keys(errors) as (keyof typeof errors)[])
    .map((item) => {
      // todo: I'm not entirely sure why I'm doing this. Why not go ahead
      // to let all the field errors show instead of restricting it? 🤔
      const fieldsToCheck = [
        "dob",
        "type",
        "time",
        "gender",
        "doctor",
        "lastName",
        "symptoms",
        "firstName",
        "providerId",
        "pharmacyCode",
        "pharmacyName",
        "companyId",
        "isDelivery",
      ];

      if (!fieldsToCheck.includes(item)) return null;

      return [item, errors[item]?.message];
    })
    .filter(Boolean);

  return (
    <div className="text-red-500 mt-2">
      {fieldErrorsTuple?.map((item) => {
        const [field, error] = item || [];

        return (
          <div key={field} className="text-xs">
            {error}
          </div>
        );
      })}
    </div>
  );
};

function LoadingPharmacies() {
  return (
    <div className="flex flex-col items-center gap-1">
      <SpinnerLoader className="w-5 h-5" />
      <span className="text-gray-500 text-xs">Loading pharmacies</span>
    </div>
  );
}
