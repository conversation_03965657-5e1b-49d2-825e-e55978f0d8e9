import { useState } from "react";
import classNames from "classnames";
import { Header } from "./Header";
import { navigationItems } from "@/lib/mock-data";
import { SideNavigation } from "./SideNavigation";
import { Link, useRouterState } from "@tanstack/react-router";
import { ConfirmSignoutModal } from "@/components/modals/ConfirmSignoutModal";

export const DashboardLayout = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const routerState = useRouterState();
  const pathname = routerState.location.pathname;
  const [confirmModal, setConfirmModal] = useState(false);

  return (
    <div className="h-screen w-screen flex bg-white">
      {/* SIDE NAVIGATION */}
      <div className="bg-white hidden md:block">
        <SideNavigation />
      </div>

      {/* MAIN CONTAINER */}
      <div className=" h-screen w-full bg-white">
        {/* HEADER */}
        <div className="h-[80px] lg:h-[118px] bg-white border-b sticky top-0 z-10">
          <Header />
        </div>

        {/* PAGES */}
        <main className="h-[calc(100vh-80px)] lg:h-[calc(100vh-118px)] overflow-y-auto overflow-x-hidden">
          <div className=" p-7">{children}</div>
        </main>

        {/* MOBILE NAVIGATION */}
        <ul className="md:hidden w-full h-[80px] bg-white sticky bottom-0 shadow-lg flex justify-between items-center p-7 ">
          {navigationItems.map((item, idx) => {
            const isActive = pathname === item.path;

            return item?.name === "Sign out" ? (
              <li
                key={`${item.name}-${idx}`}
                className={classNames(
                  "flex items-center text-sm font-semibold space-x-2 cursor-pointer border rounded-lg p-3 hover:bg-[linear-gradient(0deg,_rgba(41,207,214,0.1),_rgba(41,207,214,0.1)),linear-gradient(0deg,_#EEF1F1,_#EEF1F1)] transform transition ease-in-out duration-300",
                  {
                    "bg-[linear-gradient(0deg,_rgba(41,207,214,0.1),_rgba(41,207,214,0.1)),linear-gradient(0deg,_#EEF1F1,_#EEF1F1)] text-black border-[#F9FAFB]":
                      isActive,
                    "text-[#586579] border-transparent": !isActive,
                  },
                )}
                onClick={() => setConfirmModal(true)}
              >
                <item.icon
                  className={classNames("group-hover:fill-primary ", {
                    "fill-primary": isActive,
                    "fill-black": !isActive,
                  })}
                />
              </li>
            ) : (
              <Link
                key={`${item.name}-${idx}`}
                to={item?.path}
                className="block"
              >
                <li
                  key={`${item.name}-${idx}`}
                  className={classNames(
                    "flex items-center text-sm font-semibold space-x-2 cursor-pointer border rounded-lg mx-auto p-3 hover:bg-[linear-gradient(0deg,_rgba(41,207,214,0.1),_rgba(41,207,214,0.1)),linear-gradient(0deg,_#EEF1F1,_#EEF1F1)] transform transition ease-in-out duration-300",
                    {
                      "bg-[linear-gradient(0deg,_rgba(41,207,214,0.1),_rgba(41,207,214,0.1)),linear-gradient(0deg,_#EEF1F1,_#EEF1F1)] text-black border-[#F9FAFB]":
                        isActive,
                      "text-[#586579] border-transparent": !isActive,
                    },
                  )}
                  title={item?.name}
                >
                  <item.icon
                    className={classNames("group-hover:fill-primary ", {
                      "fill-primary": isActive,
                      "fill-black": !isActive,
                    })}
                  />
                </li>
              </Link>
            );
          })}
        </ul>
      </div>
      <ConfirmSignoutModal open={confirmModal} setOpen={setConfirmModal} />
    </div>
  );
};
