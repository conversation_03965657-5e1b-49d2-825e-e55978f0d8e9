import { Consultation<PERSON>imeField } from "@/components/consultation-time-field";
import { DoctorStarRating } from "@/components/doctor-star-rating";
import { SymptomSelectorInput } from "@/components/symptom-selector-input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { useCreateConsultation } from "@/features/consultation/hooks/use-create-consultation";
import { useHospitalUserInfoStore } from "@/features/hospital-consultation/hooks/use-hospital-user-info-store";
import { HospitalDoctor } from "@/features/consultation/types";
import { formatName, formatToNaira } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { ChevronLeft } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useWidgetColor } from "@/hooks/useWidgetColor";

const steps = ["booking-preference-step", "summary-step"] as const;
type Step = (typeof steps)[number];

type ConsultationDrawerProps = {
  doctor: HospitalDoctor;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDate: Date;
  planAmount?: number;
  providerId?: string;
};

export function ConsultationDrawer(props: ConsultationDrawerProps) {
  const { doctor, isOpen, selectedDate, onOpenChange, planAmount, providerId } =
    props;

  const [step, setStep] = useState<Step>(steps[0]);

  const [formState, setFormState] = useState<BookingFormSchemaType>(
    {} as BookingFormSchemaType,
  );

  const [submitting, setSubmitting] = useState(false);

  const userInfoStore = useHospitalUserInfoStore();

  const { createConsultation } = useCreateConsultation();

  const { toast } = useToast();

  async function onSubmit(data: BookingFormSchemaType) {
    setSubmitting(true);

    if (!(planAmount && providerId)) {
      console.log("planAmount or providerId is undefined");
      toast({
        title: "Critical error!",
        description:
          "Plan amount or provider id is missing. Please contact support",
        variant: "destructive",
      });
      setSubmitting(false);
      return;
    }

    await createConsultation({
      firstName: userInfoStore.firstName,
      lastName: userInfoStore.lastName,
      gender: userInfoStore.gender,
      phoneNumber: userInfoStore.phoneNumber,
      email: userInfoStore.email,

      date: selectedDate,
      time: data.time,

      symptoms: data.symptoms,
      discomfortLevel: data.discomfortLevel,
      firstNotice: data.firstNotice,
      channel: data.channel,

      doctorId: doctor?._id,
      amount: planAmount,
      providerId,
      consultationType: "scheduled",
      createdThrough: "hospital_direct",
    });

    setSubmitting(false);
  }

  if (!doctor) return null;

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="max-w-full w-full md:max-w-lg overflow-y-scroll">
        {step === "booking-preference-step" && (
          <BookingFormStep
            selectedDate={selectedDate}
            doctor={doctor}
            onFormSubmit={(data) => {
              setFormState(data);
              setStep(steps[1]);
            }}
            formState={formState}
            onCancel={() => {
              onOpenChange(false);
            }}
          />
        )}

        {step === "summary-step" && (
          <SummaryStep
            doctor={doctor}
            formState={formState}
            onBack={() => setStep(steps[0])}
            onSubmit={() => {
              onSubmit(formState);
            }}
            submitting={submitting}
            selectedDate={selectedDate}
            planAmount={planAmount}
          />
        )}
      </SheetContent>
    </Sheet>
  );
}

const BookingFormSchema = z.object({
  time: z.string().min(2, "Required"),
  symptoms: z
    .array(z.object({ name: z.string() }))
    .min(1, { message: "This field is required!" }),
  discomfortLevel: z.enum(["None", "Mild", "Medium", "Intense"]),
  firstNotice: z.enum(["This week", "Last week", "One month ago"]),
  channel: z.enum(["video", "chat", "voice"]),
});

type BookingFormSchemaType = z.infer<typeof BookingFormSchema>;

type BookingFormStepProps = {
  selectedDate: Date;
  doctor: HospitalDoctor;
  onFormSubmit: (data: BookingFormSchemaType) => void;
  formState: Partial<BookingFormSchemaType>;
  onCancel?: () => void;
};

function BookingFormStep(props: BookingFormStepProps) {
  const { selectedDate, doctor, onFormSubmit, formState, onCancel } = props;

  const form = useForm<BookingFormSchemaType>({
    resolver: zodResolver(BookingFormSchema),
    defaultValues: {
      time: formState.time || "",
      symptoms: formState.symptoms || [],
      discomfortLevel: formState.discomfortLevel,
      firstNotice: formState.firstNotice,
      channel: formState.channel,
    },
  });

  const onSubmit = (values: BookingFormSchemaType) => {
    onFormSubmit(values);
  };

  return (
    <div className="mt-8">
      <Doctor doctor={doctor} />
      <div className="pt-10 pb-10">
        <h2 className="text-2xl font-medium">Booking Preference</h2>
        <p className="text-tertiary mt-0.5 mb-3 text-sm">
          Pick a suitable time for your consultation
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="time"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Time</FormLabel>
                  <ConsultationTimeField
                    selectedDate={selectedDate}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    defaultValue={field.value}
                    doctorProfileId={doctor?._id}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <h2 className="text-2xl font-medium mt-6 lg:mt-10">
              Symptom Information
            </h2>
            <p className="text-tertiary mt-0.5 mb-3 text-sm">
              Tell us what the problem is
            </p>

            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="symptoms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>What are your symptoms?</FormLabel>
                    <FormControl className="w-full">
                      <SymptomSelectorInput
                        onChange={field.onChange}
                        initialValues={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discomfortLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>What is your discomfort level</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="None">None</SelectItem>
                        <SelectItem value="Mild">Mild</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="Intense">Intense</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="firstNotice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      When did you start experiencing the symptoms?
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="This week">This week</SelectItem>
                        <SelectItem value="Last week">Last week</SelectItem>
                        <SelectItem value="One month ago">
                          One month ago
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="channel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Communication Channel</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="chat">Chat</SelectItem>
                        <SelectItem value="voice">Voice</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex flex-col-reverse lg:flex-row gap-3 mt-8 lg:mt-12">
              <Button
                variant="outlinePrimary"
                className="flex-1"
                onClick={onCancel}
              >
                Cancel
              </Button>

              <Button type="submit" className="flex-1">
                Continue
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}

type SummaryStepProps = {
  formState: Partial<BookingFormSchemaType>;
  selectedDate: Date;
  doctor: HospitalDoctor;
  onBack: () => void;
  onSubmit: () => void;
  submitting: boolean;
  planAmount?: number;
};

function SummaryStep(props: SummaryStepProps) {
  const {
    formState,
    doctor,
    onBack,
    onSubmit,
    submitting,
    selectedDate,
    planAmount,
  } = props;

  const color = useWidgetColor();

  return (
    <div className="mt-6">
      <div className="">
        <Button
          variant="ghost"
          onClick={onBack}
          className="pl-0 text-black font-normal text-base"
        >
          <ChevronLeft className="" />
          <span>Previous</span>
        </Button>
      </div>

      <h2 className="text-2xl font-medium">Appointment Summary</h2>

      <div className="mt-6">
        <Doctor doctor={doctor} />

        <div className="flex flex-wrap gap-1.5 mt-4 lg:mt-2 lg:pl-[72px]">
          <Pill text={format(selectedDate, "do MMMM yyyy")} />
          <Pill text={formState.time || ""} />
          <Pill text="30 mins" />
          <Pill text={formState.channel || ""} />
        </div>

        <div className="flex flex-col gap-8 mt-6">
          <DisabledInputLike
            label="What are your symptoms?"
            text={
              formState.symptoms?.reduce(
                (acc, curr) => `${acc} ${curr.name},`,
                "",
              ) || ""
            }
          />
          <DisabledInputLike
            label="What is your discomfort level? "
            text={formState.discomfortLevel || ""}
          />
          <DisabledInputLike
            label="When did you start experiencing the symptoms?"
            text={formState.firstNotice || ""}
          />
        </div>

        <div className="flex justify-between mt-10">
          <p className="font-medium text-2xl">Consultation Fee: </p>
          <p style={{ color }} className="font-medium text-2xl text-primary">
            {formatToNaira(planAmount || 0) || "N / A"}
          </p>
        </div>

        <div className="flex flex-col-reverse lg:flex-row gap-3 mt-8 lg:mt-12 pb-12">
          <Button variant="outlinePrimary" className="flex-1" onClick={onBack}>
            Back
          </Button>

          <Button className="flex-1" onClick={onSubmit} loading={submitting}>
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}

type DoctorProps = {
  doctor: HospitalDoctor;
};

function Doctor(props: DoctorProps) {
  const { doctor } = props;
  const doctorName = formatName(doctor?.firstName, doctor?.lastName);

  return (
    <div className="flex gap-2">
      <img
        src={doctor?.idCard}
        className="rounded-full h-[65px] w-[65px] object-cover"
        alt={`${doctorName} image`}
      />
      <div>
        <p className="font-medium text-xl">{`${doctorName}`}</p>
        <p className="font-normal">{`${doctor?.cadre || "Nil"}`}</p>
        {doctor.rating ? (
          <div className="flex items-center gap-1">
            <DoctorStarRating rating={doctor?.rating || 0} />
            <p className="text-neutral-300 text-sm">
              {(doctor.rating || 0).toFixed(1)}
            </p>
          </div>
        ) : null}
      </div>
    </div>
  );
}

function Pill(props: { text: string }) {
  return (
    <div className="flex items-center justify-center gap-2 border border-slate-300 rounded-full px-6 py-2">
      <p className="text-black text-sm">{props.text}</p>
    </div>
  );
}

function DisabledInputLike(props: { text: string; label: string }) {
  return (
    <div className="flex flex-col gap-1">
      <p className="">{props.label}</p>
      <div className="flex items-center gap-2 bg-gray-100 rounded-lg px-4 py-3">
        <p className="text-sm">{props.text}</p>
      </div>
    </div>
  );
}
