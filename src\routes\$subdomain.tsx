import { DefaultPage } from "@/components/default-page";
import { SpinnerLoader } from "@/components/spinner-loader";
import { env } from "@/config/env";
import { useDoctorData } from "@/features/doctor-consultation/hooks/useDoctorData";
import { getPartnerBySubdomainQuery } from "@/graphql/queries";
import { useQuery as useApolloQuery } from "@apollo/client";
import {
  createFileRoute,
  useNavigate,
  useSearch,
} from "@tanstack/react-router";

export const Route = createFileRoute("/$subdomain")({
  component: RouteComponent,
});

/*
 *  If subdomain is a valid id for either a hospital, doctor or HMO, navigate to appropriate route, else show default page.
 */

const tenantType = {
  hmo: "hmo",
  hospital: "hospital",
  doctor: "doctor",
};

function RouteComponent() {
  const { subdomain } = Route.useParams();
  const isDev = import.meta.env.DEV;
  const isStaging = env.MODE === "PREVIEW";
  const stagingPrefix = isStaging ? "-staging" : "";
  const currentHost = window.location.hostname;
  const navigate = useNavigate({ from: Route.id });
  const searchParams = useSearch({ strict: false });

  const { doctorProfile, isLoading: doctorDataLoading } = useDoctorData({
    profileId: subdomain,
  });
  const { data: partnerData, loading: partnerDataLoading } = useApolloQuery(
    getPartnerBySubdomainQuery,
    {
      variables: {
        subdomain,
      },
    },
  );

  const loading = doctorDataLoading || partnerDataLoading;

  if (loading) {
    return <LoadingView />;
  }

  // Check if subdomain maps to a doctor profile
  if (doctorProfile) {
    const expectedHost = `doctor-direct${stagingPrefix}.heala.io`;
    if (isDev || currentHost === expectedHost) {
      navigate({
        to: "/doctor/$profileId",
        params: { profileId: subdomain },
        search: searchParams,
      });
    } else {
      window.location.href = `https://${expectedHost}/${encodeURIComponent(
        subdomain,
      )}`;
    }
    return null;
  }

  // Check if subdomain maps to a hospital
  if (partnerData?.getPartnerBySubdomain?.category === tenantType.hospital) {
    const expectedHost = `hospital${stagingPrefix}.heala.io`;
    if (isDev || currentHost === expectedHost) {
      navigate({
        to: "/hospital/$subdomain",
        params: { subdomain },
        search: searchParams,
      });
    } else {
      window.location.href = `https://${expectedHost}/${encodeURIComponent(
        subdomain,
      )}`;
    }
    return null;
  }

  // Check if subdomain maps to an HMO
  if (partnerData?.getPartnerBySubdomain?.category === tenantType.hmo) {
    const expectedHost = `hmo${stagingPrefix}.heala.io`;
    if (isDev || currentHost === expectedHost) {
      navigate({
        to: "/hmo/$subdomain",
        params: { subdomain },
        search: searchParams,
      });
    } else {
      window.location.href = `https://${expectedHost}/${encodeURIComponent(
        subdomain,
      )}`;
    }
    return null;
  }

  return <DefaultPage />;
}

function LoadingView() {
  return (
    <div className="flex items-center justify-center h-screen">
      <SpinnerLoader />
    </div>
  );
}
