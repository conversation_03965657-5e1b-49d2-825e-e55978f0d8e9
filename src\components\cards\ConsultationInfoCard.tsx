import { useState, useEffect } from "react";
import { Badge } from "../ui/badge";
import { addMinutes } from "date-fns";
import { Button } from "../ui/button";
import { Skeleton } from "../ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  ConsultationInfoCardBtnType,
  ConsultationInfoCardType,
} from "@/lib/typescript/types";
import {
  calculateRemainingTime,
  checkStatus,
  getFormattedTimeRemaining,
  isWaitTimeReached,
} from "@/lib/utils";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { env } from "@/config/env";

export const DoctorInfoCard = ({
  name,
  image,
  imgFallback,
  specialization,
  rating,
}: {
  name: string;
  image: string | null;
  imgFallback: string;
  specialization: string;
  rating: string;
}) => {
  return (
    <div className="flex items-center space-x-3">
      <Avatar className="w-16 h-16 flex justify-center items-center rounded-full bg-gray-200">
        <AvatarImage src={image || ""} alt={name} />
        <AvatarFallback>{imgFallback}</AvatarFallback>
      </Avatar>

      <div className="space-y-1">
        <p className="text-xl text-black font-medium">{name}</p>
        <p className="text-black text-sm">{specialization}</p>
        <p className="text-xs text-black">{rating}</p>
      </div>
    </div>
  );
};

export const ConsultationInfoCard = ({
  consultationInfo,
}: {
  consultationInfo: ConsultationInfoCardType;
}) => {
  const {
    doctorName,
    doctorImage,
    doctorImgFallback,
    doctorSpecialization,
    doctorRating,
    date,
    time,
    medium,
    fee,
    type,
    dateString,
  } = consultationInfo;

  const color = useWidgetColor();

  return (
    <div className="border border-[#EDEDED] p-5 rounded-lg space-y-10">
      <DoctorInfoCard
        name={doctorName}
        image={doctorImage || ""}
        imgFallback={doctorImgFallback}
        specialization={doctorSpecialization}
        rating={doctorRating}
      />
      <div className="flex gap-3 flex-wrap font-medium">
        <Badge className="border border-[#EDEDED] bg-white text-[#0C1322]">
          {date}
        </Badge>
        <Badge className="border border-[#EDEDED] bg-white text-[#0C1322]">
          {time}
        </Badge>
        <Badge className="border border-[#EDEDED] bg-white text-[#0C1322]">
          {medium}
        </Badge>
      </div>

      <div className="space-y-1">
        <p className="text-black font-medium space-x-2">
          <span>Consultation Fee:</span>
          <span style={{ color }}>{fee}</span>
        </p>
        <div className="text-black font-medium space-x-2">
          <span>Type:</span>
          <Badge className="border border-[#EEB76B] bg-[#FFF5E7] text-[#EEB76B]">
            {type}
          </Badge>
        </div>
      </div>
      <div className="space-y-1">
        <ConsultationInfoCardBtn
          time={dateString}
          consultationId={consultationInfo?._id}
        />
      </div>
    </div>
  );
};

export const ConsultationInfoCardSkeleton = () => {
  return (
    <div className="border border-[#EDEDED] p-5 rounded-lg space-y-10">
      <div className="flex items-center space-x-3">
        <Skeleton className="w-16 h-16 rounded-full" />

        <div className="space-y-1">
          <Skeleton className="w-20 h-4" />
          <Skeleton className="w-24 h-4" />
          <Skeleton className="w-8 h-4" />
        </div>
      </div>
      {/*  */}
      <div className="flex gap-3 font-medium">
        <Skeleton className="w-full h-4" />
        <Skeleton className="w-full h-4" />
        <Skeleton className="w-full h-4" />
      </div>

      <div className="space-y-1">
        <Skeleton className="w-full h-8" />
        <Skeleton className="w-1/2 h-8" />
      </div>
      <div className="space-y-1">
        <Skeleton className="w-full h-14" />
      </div>
    </div>
  );
};

const ConsultationInfoCardBtn: React.FC<ConsultationInfoCardBtnType> = ({
  time,
  consultationId,
}) => {
  const targetTime = new Date(time);
  const [remainingTime, setRemainingTime] = useState<string | null>(null);
  const [status, setStatus] = useState<
    "COUNTING_DOWN" | "EXTRA_COUNTDOWN" | "EXPIRED" | null
  >(null);

  useEffect(() => {
    setStatus(checkStatus(time));
  }, [time]);

  // FOR INITIAL COUNTDOWN
  useEffect(() => {
    if (status === "EXPIRED" || status === "EXTRA_COUNTDOWN") return;

    const interval = setInterval(() => {
      const remainingTimeObj = calculateRemainingTime(
        addMinutes(targetTime, 0),
      );
      const remainingTime = remainingTimeObj
        ? getFormattedTimeRemaining(remainingTimeObj)
        : null;
      setRemainingTime(remainingTime);

      if (isWaitTimeReached(remainingTimeObj)) setStatus("EXTRA_COUNTDOWN");

      if (remainingTime === null) {
        clearInterval(interval);
        setStatus("EXPIRED");
      }
    }, 1000);

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  // FOR EXTRA-TIME COUNTDOWN
  useEffect(() => {
    if (status === "EXPIRED" || status === "COUNTING_DOWN" || !status) return;

    const interval = setInterval(() => {
      const remainingTimeObj = calculateRemainingTime(
        addMinutes(targetTime, 10),
      );

      const remainingTime = remainingTimeObj
        ? getFormattedTimeRemaining(remainingTimeObj)
        : null;
      setRemainingTime(remainingTime);

      if (remainingTime === null) {
        clearInterval(interval);
        setStatus("EXPIRED");
      }
    }, 1000);

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  return (
    <div className="flex flex-col items-center space-y-4">
      <Button
        className="w-full flex flex-col"
        onClick={() => {
          if (status === "EXTRA_COUNTDOWN")
            window.open(
              `${env.CONSULTATION_BASE_URL}${consultationId}`,
              "_blank",
            );
        }}
        disabled={status !== "EXTRA_COUNTDOWN"}
      >
        {status === "COUNTING_DOWN" ? (
          `Starts in ${remainingTime}`
        ) : status === "EXPIRED" ? (
          "Missed Consultation"
        ) : (
          <>
            <span className="">Join Consultation {remainingTime}</span>
          </>
        )}
      </Button>
      {/* {status === "EXPIRED" && (
        <p className="text-xs text-center text-red-500">
          You missed this consultation, click the button above if you would like
          to reschedule.
        </p>
      )} */}
    </div>
  );
};
