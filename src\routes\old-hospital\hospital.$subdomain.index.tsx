import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { Head } from '@/components/head'
import HealaLogoIcon from '@/components/icons/heala-logo-icon'
import { Button } from '@/components/ui/button'
import { usePartnerData } from '@/features/consultation/hooks/use-partner-data'
import { usePartnerPlansData } from '@/features/consultation/hooks/use-partner-plans-data'
import { FullScreenSpinnerLoader } from '@/components/full-screen-spinner-loader'

export const Route = createFileRoute('/old-hospital/hospital/$subdomain/')({
  component: RouteComponent,
})

function RouteComponent() {
  const { subdomain } = Route.useParams()
  const {
    partnerData,
    providerId,
    loading: partnerDataLoading,
  } = usePartnerData({
    subdomain,
  })
  const { plansData, loading: plansDataLoading } = usePartnerPlansData({
    providerId,
  })

  const loading = partnerDataLoading || plansDataLoading

  const { widgetLogo } = partnerData || {}

  if (loading) return <FullScreenSpinnerLoader />

  return (
    <>
      <Head title={subdomain || undefined} withoutSuffix />
      <div className="flex flex-col sm:w-[70%] lg:max-w-[1076px] mx-auto px-4 pt-12">
        {/* logo */}
        <div className="flex justify-center">
          {widgetLogo ? (
            <img
              src={widgetLogo}
              className="max-h-[64px] max-w-[160px] md:max-w-[200px] object-contain"
              width={160}
              height={45}
            />
          ) : (
            <HealaLogoIcon className="w-16 h-16" />
          )}
        </div>

        {/* hospital cover image */}
        <div className="h-[172px] lg:h-[300px] mt-12 rounded-md overflow-hidden">
          <img
            className="object-cover h-full w-full"
            src="/hospital-landing-banner.png"
            alt="Healthy family with kid smilling"
          />
        </div>

        <div className="w-full max-w-[356px] text-center mt-12 mx-auto">
          <h2 className="text-4xl font-bold ">Talk to a Doctor</h2>
          <p className="max-w-[276px] mx-auto mt-6 text-[#A6A6A6]">
            Experience the comfort of receiving medical advice from your home.
            Available 24/7.
          </p>

          <div className="mt-12 pb-12 w-full space-y-6 flex flex-col">
            <Button
              asChild
              disabled={!plansData?.length}
              variant={!plansData?.length ? 'disabled' : undefined}
            >
              <Link to="/hospital/$subdomain/start" params={{ subdomain }}>
                BOOK APPOINTMENT
              </Link>
            </Button>
            <Button variant="outline" disabled={!plansData?.length}>
              <Link to="/access">VIEW CONSULTATIONS</Link>
            </Button>
            {!plansData?.length && (
              <p className="text-red-500 text-sm">
                No plan available. Please contact your provider
              </p>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

// function LoadingView() {
//   return (
//     <div className="flex items-center justify-center h-screen">
//       <Loader2 className="w-10 h-10 animate-spin text-primary" />
//     </div>
//   );
// }
