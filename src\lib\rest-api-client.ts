import { env } from "@/config/env";
import { API_KEY_LS_KEY } from "@/lib/constants";
import Axios from "axios";

export const restApi = Axios.create({
  baseURL: `${env.API_BASE_URL}/rest`,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// each product loads API key to sessionStorage

restApi.interceptors.request.use((config) => {
  const apiKey = sessionStorage.getItem(API_KEY_LS_KEY);

  if (config.headers) {
    config.headers.Authorization = `Api-Key ${apiKey}`;
  }
  return config;
});
