import { Head } from "@/components/head";
import LeftArrowIcon from "@/components/icons/left-arrow-icon";
import { ConsultationFormsContainer } from "@/features/doctor-consultation/components/consultation-forms-container";
import { DoctorStarRating } from "@/components/doctor-star-rating";
import { ErrorView } from "@/features/doctor-consultation/components/error-view";
import { LoadingView } from "@/features/doctor-consultation/components/loading-view";
import { useDoctorData } from "@/features/doctor-consultation/hooks/useDoctorData";
import {
  FormStepsProvider,
  useDoctorConsultationFormSteps,
} from "@/features/doctor-consultation/hooks/useDoctorConsultationFormSteps";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import { API_KEY_LS_KEY } from "@/lib/constants";
import { env } from "@/config/env";
import { makeConsultationDoctor } from "@/lib/factories";

export const Route = createFileRoute("/doctor_/$profileId/book-consultation")({
  component: RouteComponent,
});

function RouteComponent() {
  const { profileId } = Route.useParams();

  const { doctorProfile, isLoading, error, isProfileNotFound } = useDoctorData({
    profileId,
  });

  const doctor = makeConsultationDoctor(doctorProfile);

  useEffect(() => {
    // need to do this here because /doctor routes don't use nested routes at the moment so
    // there's no overarching parent route that always runs this.
    // will move when I've re-arranged
    sessionStorage.setItem(API_KEY_LS_KEY, env.HEALA_PROVIDER_API_KEY);
  }, []);

  return (
    <>
      <Head
        title={
          doctorProfile
            ? `Dr. ${doctorProfile?.firstName} ${doctorProfile?.lastName} - Book consultation`
            : undefined
        }
      />
      <div>
        <div className="min-h-screen flex justify-center sm:items-center bg-primary-50 px-4 py-4">
          <div className="w-full max-w-[420px] lg:w-[912px] lg:max-w-none">
            {isLoading ? (
              <LoadingView />
            ) : error || isProfileNotFound || !doctor ? (
              <ErrorView isNotFoundError={isProfileNotFound} />
            ) : (
              <FormStepsProvider>
                <BackButton className="lg:hidden mb-4" />
                <div className="flex flex-col lg:flex-row w-full lg:h-[560px] bg-white border  border-neutral-100 rounded-sm lg:rounded-2xl overflow-hidden">
                  {/* doctor info */}
                  <div className="hidden lg:block pt-6 px-4 border-r border-r-neutral-100 lg:shrink-0 overflow-hidden">
                    <BackButton className="cursor-pointer" />
                    <div className="mt-6 w-[249px] h-[260px] bg-neutral-100 rounded-lg overflow-hidden">
                      <img
                        className="w-full h-full object-contain lg:object-cover object-center rounded-lg overflow-hidden"
                        src={doctorProfile?.picture || ""}
                        alt={`Dr. ${doctorProfile?.firstName} ${doctorProfile?.lastName} photo`}
                      />
                    </div>

                    <h1 className="text-xl font-medium mt-4">
                      Dr.{" "}
                      {`${doctorProfile?.firstName} ${doctorProfile?.lastName}`}
                    </h1>

                    <div className="space-y-2 lg:space-y-2 mt-2">
                      <p className="text-tertiary text-sm">
                        Specialty: {doctorProfile?.specialization}
                      </p>
                      <p className="text-tertiary text-sm">
                        HEALA ID: {doctorProfile?.dociId}
                      </p>
                      <div className="flex items-center gap-1 mt-4">
                        <p className="text-neutral-300 text-sm">
                          {(doctorProfile?.rating || 0).toFixed(1)}
                        </p>
                        <DoctorStarRating rating={doctorProfile?.rating || 0} />
                      </div>
                    </div>

                    <div className="flex items-center gap-2 lg:w-fit bg-primary-50 border border-neutral-100 px-2 py-2 mt-2 rounded-lg">
                      <p className="text-sm">Consultation fee: </p>
                      {doctorProfile?.fee ? (
                        <p className="text-primary font-medium">
                          N{doctorProfile?.fee}
                        </p>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>

                  <div className="flex-1 flex lg:min-h-none py-6 px-4 lg:py-7 lg:px-11">
                    <ConsultationFormsContainer doctorProfile={doctor} />
                  </div>
                </div>
              </FormStepsProvider>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

type BackButtonProps = {
  className?: string;
};

function BackButton(props: BackButtonProps) {
  const { className } = props;
  const { goBack, hasPreviousStep } = useDoctorConsultationFormSteps();
  const navigate = useNavigate();
  const { profileId } = Route.useParams();

  function goToPreviousStepOrDoctorProfile() {
    if (hasPreviousStep) {
      goBack();
    } else {
      navigate({
        to: "/doctor/$profileId",
        params: { profileId },
      });
    }
  }
  return (
    <LeftArrowIcon
      className={className}
      onClick={goToPreviousStepOrDoctorProfile}
    />
  );
}
