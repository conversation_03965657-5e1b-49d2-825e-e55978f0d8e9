import * as yup from "yup";
import { gql } from "@/graphql/generated";
import { useMutation } from "@apollo/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormProvider, useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  useAccessStore,
  selectSetAccessEmail,
  selectSetAccessStep,
} from "@/store/accessStore";
import { Message } from "@/components/Message";

const schema = yup
  .object({
    email: yup.string().email("Invalid email").required("Email is required"),
  })
  .required();

type FormData = yup.InferType<typeof schema>;

const sendOTPMutation = gql(`
  mutation sendOTP($email: String!) {
    sendOTP(data: { email: $email })
  }
`);

export const LoginForm = () => {
  const setAccessStep = useAccessStore(selectSetAccessStep);
  const setAccessEmail = useAccessStore(selectSetAccessEmail);

  const methods = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: { email: "" },
    mode: "onChange",
  });

  const {
    handleSubmit,
    getValues,
    formState: { isLoading: isFormLoading },
  } = methods;

  const [sendOTP, { loading: sendingOTP, error }] = useMutation(
    sendOTPMutation,
    {
      onCompleted: () => {
        setAccessEmail(getValues("email"));
        setAccessStep("otp");
      },
    },
  );

  const loading = isFormLoading || sendingOTP;

  const onSubmitForm = async (values: FormData) => {
    await sendOTP({ variables: { email: values.email } });
  };

  return (
    <div className="p-4 lg:p-10 space-y-10">
      <div className="space-y-3">
        <h1 className="text-4xl font-medium">Log in</h1>
        <p className="text-sm">
          When you log in via email, we’ll send a one-time authentication key to
          your email address. Enter the key to verify your login.
        </p>
      </div>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-5">
          <FormField
            control={methods.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {error && <Message type="error" message={error?.message} />}
          <Button
            loading={loading}
            disabled={loading}
            type="submit"
            className="w-full"
          >
            SIGN IN
          </Button>
        </form>
      </FormProvider>
    </div>
  );
};
