import React from "react";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface AddressLabelProps extends React.ComponentPropsWithoutRef<typeof Label> {
  required?: boolean;
}

export function AddressLabel({ 
  className, 
  required, 
  children, 
  ...props 
}: AddressLabelProps) {
  return (
    <Label
      className={cn(className)}
      {...props}
    >
      {children}
      {required && (
        <sup className="text-destructive text-sm font-medium ml-0.5">*</sup>
      )}
    </Label>
  );
}
