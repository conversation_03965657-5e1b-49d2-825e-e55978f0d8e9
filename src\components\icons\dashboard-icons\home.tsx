import { IconProps } from "@/lib/typescript/types";

export const HomeIcon: React.FC<IconProps> = ({ size, className }) => {
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_5641_57718)">
        <path d="M22.9745 8.6949L15.8885 1.60891C13.7391 -0.535003 10.26 -0.535003 8.11053 1.60891L1.02454 8.6949C0.36604 9.34988 -0.00305299 10.2411 -0.000474865 11.1699V21.5479C0.00172826 22.9025 1.09991 23.9997 2.45455 24.0009H21.5445C22.8991 23.9998 23.9973 22.9025 23.9995 21.5479V11.1699C24.0021 10.2411 23.633 9.34988 22.9745 8.6949ZM20.9995 21.0009H15.9995V17.8189C15.9995 15.7102 14.2901 14.0009 12.1815 14.0009H11.8175C9.70891 14.0009 7.99953 15.7102 7.99953 17.8189V21.0009H2.99952V11.1699C2.99985 11.0373 3.05225 10.9102 3.14553 10.8159L10.2315 3.72991C11.2077 2.75346 12.7906 2.75327 13.7671 3.72944C13.7672 3.72958 13.7674 3.72977 13.7675 3.72991L20.8535 10.8159C20.9467 10.9102 20.9992 11.0373 20.9995 11.1699V21.0009H20.9995Z" />
      </g>
      <defs>
        <clipPath id="clip0_5641_57718">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
