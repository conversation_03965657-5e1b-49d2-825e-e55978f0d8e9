import React, { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  checkAddress,
  checkAndFormatAddress,
  removeDuplicateCommas,
} from "@/lib/utils";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { FormLabel } from "@/components/ui/form";
import { CircleXIcon, PlusIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { AddressModal } from "@/features/hmo-consultation/components/address-modal";
import { Input } from "@/components/ui/input";

export const AddressInput = () => {
  const [addressModal, setAddressModal] = useState(false);
  const {
    watch,
    setValue,
    register,
    formState: { errors },
  } = useFormContext<FormSchemaType>();

  const address = checkAndFormatAddress(watch("address") || "");

  const isAddressComplete = useMemo(() => {
    return checkAddress(address);
  }, [address]);

  return (
    <>
      <div>
        <FormLabel required>
          Address {!isAddressComplete && "(click to add address)"}
        </FormLabel>
        {!isAddressComplete && (
          <div className="mt-2">
            <Button
              variant="outline"
              className="w-full text-primary border-primary hover:text-primary"
              onClick={() => {
                setAddressModal(true);
              }}
              type="button"
            >
              <PlusIcon className="w-5" />
              <span>Add Address</span>
            </Button>
          </div>
        )}

        {isAddressComplete && (
          <div className="relative">
            <Input
              disabled
              {...register("address")}
              className="text-base lg:text-xs cursor-auto"
            />
            <span
              className="absolute -right-1.5 -top-3 text-xs text-red-500 cursor-pointer"
              onClick={() => {
                setValue("address", "", {
                  shouldValidate: true,
                });
                setValue("pharmacyCode", "");
              }}
            >
              <CircleXIcon className="w-4" />
            </span>
          </div>
        )}

        {errors.address && (
          <p className="text-xs text-red-500 mt-1">{errors.address.message}</p>
        )}
      </div>

      <AddressModal
        open={addressModal}
        onClose={() => setAddressModal(false)}
        addAddress={(addressObj) => {
          const address = `${addressObj?.lga}, ${addressObj?.state}.`; //${addressObj?.street},
          setValue("address", removeDuplicateCommas(address));
        }}
      />
    </>
  );
};
