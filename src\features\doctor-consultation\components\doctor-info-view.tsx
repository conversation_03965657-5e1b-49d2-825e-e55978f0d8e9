import { Head } from "@/components/head";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { LoadingView } from "./loading-view";

import { Link } from "@tanstack/react-router";

import { useDoctorData } from "../hooks/useDoctorData";
import { ErrorView } from "@/features/doctor-consultation/components/error-view";
import { DoctorStarRating } from "@/components/doctor-star-rating";
import { env } from "@/config/env";
import { formatName } from "@/lib/utils";

type DoctorInfoViewProps = {
  profileId: string;
};

export function DoctorInfoView(props: DoctorInfoViewProps) {
  const { profileId } = props;

  const { doctorProfile, isLoading, error, isProfileNotFound } = useDoctorData({
    profileId,
  });

  const providerId = doctorProfile?.providerId?._id || "";
  const doctorName = formatName(
    doctorProfile?.firstName,
    doctorProfile?.lastName,
    "Dr.",
  );

  return (
    <>
      <Head
        title={
          doctorProfile
            ? `Dr. ${doctorProfile?.firstName} ${doctorProfile?.lastName} - Book consultation`
            : undefined
        }
      />
      <div className="min-h-screen flex justify-center sm:items-center bg-primary-50 px-4 py-4">
        <div className="w-full max-w-[420px] lg:w-[912px] lg:max-w-none lg:min-h-[492px]">
          {isLoading ? (
            <LoadingView />
          ) : error || isProfileNotFound ? (
            <ErrorView isNotFoundError={isProfileNotFound} />
          ) : (
            <>
              <div className="flex flex-col lg:flex-row lg:items-stretch w-full lg:h-full border border-neutral-100 rounded-lg overflow-hidden">
                {/* doctor image */}
                <div className="h-[300px] lg:h-auto lg:w-[338px] lg:shrink-0 overflow-hidden bg-neutral-100">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <img
                      className="object-contain lg:object-cover object-center h-full w-full"
                      src={doctorProfile?.picture || ""}
                      alt={`${doctorName} photo`}
                    />
                  )}
                </div>

                {/* doctor info */}
                <div className="bg-white px-4 py-5 lg:px-10 lg:py-8">
                  <h1 className="text-xl font-medium">{doctorName}</h1>

                  <div className="flex items-center gap-2 lg:w-fit bg-primary-50 border border-neutral-100 px-2 py-2 lg:px-4 lg:py-2 mt-4 rounded-lg">
                    <p className="text-sm lg:text-base">Consultation fee: </p>
                    {doctorProfile?.fee ? (
                      <p className="text-primary font-medium lg:text-xl">
                        N{doctorProfile?.fee}
                      </p>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="space-y-2 lg:space-y-4 mt-4 lg:mt-6">
                    <p className="text-tertiary text-sm">
                      Specialty: {doctorProfile?.specialization}
                    </p>
                    <p className="text-tertiary text-sm">
                      HEALA ID: {doctorProfile?.dociId}
                    </p>
                    <div className="flex items-center gap-1 mt-4">
                      <p className="text-neutral-300 text-sm">
                        {(doctorProfile?.rating || 0).toFixed(1)}
                      </p>
                      <DoctorStarRating rating={doctorProfile?.rating || 0} />
                    </div>
                  </div>

                  <div className="mt-4 lg:mt-6 rounded-lg bg-primary-50 px-3 py-4 lg:px-6 border border-primary">
                    <p className="text-primary">
                      With 24/7 access to online doctors, care is always
                      available, anytime and anywhere. Select and see your
                      favorite providers again and again, right from your
                      smartphone, tablet or computer
                    </p>
                  </div>

                  {/* desktop view CTAs */}
                  <div className="hidden lg:block mt-6">
                    <CTAs providerId={providerId} doctorName={doctorName} />
                  </div>
                </div>
              </div>

              {/* mobile view CTAs */}
              <div className="lg:hidden mt-10">
                <CTAs providerId={providerId} doctorName={doctorName} />
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}

function CTAs({
  providerId,
  doctorName,
}: {
  providerId: string;
  doctorName: string;
}) {
  const btnText = "Start consultation";
  const healaProvider = env.HEALA_PROVIDER_ID;
  const canConsult = healaProvider === providerId;
  const btnVariant = canConsult ? "default" : "disabled";

  console.log(healaProvider, providerId, canConsult, btnVariant);

  return (
    <div className="flex flex-col gap-2 text-center">
      <Button variant={btnVariant} disabled={!canConsult}>
        {canConsult ? (
          <Link from="/doctor/$profileId" to="./book-consultation">
            {btnText}
          </Link>
        ) : (
          <>{btnText}</>
        )}
      </Button>
      {!canConsult && (
        <p className="text-red-500 text-xs">{`${doctorName} is not allowed to have 1-on-1 consultations.`}</p>
      )}
      <Button variant="ghost">
        <Link to="/access">View previous consultations</Link>
      </Button>
    </div>
  );
}
