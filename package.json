{"name": "weblinks", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen -w"}, "dependencies": {"@apollo/client": "^3.11.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.1", "@mui/material": "^6.3.1", "@mui/x-date-pickers": "^7.23.3", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@tanstack/react-query": "^5.61.5", "@tanstack/react-router": "^1.81.4", "@tanstack/zod-adapter": "^1.82.1", "@tsparticles/confetti": "^3.6.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "es-toolkit": "^1.27.0", "graphql": "^16.9.0", "input-otp": "^1.4.1", "libphonenumber-js": "^1.11.17", "lucide-react": "^0.454.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.9", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "yup": "^1.5.0", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@0no-co/graphqlsp": "^1.12.16", "@eslint/js": "^9.13.0", "@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/client-preset": "^4.5.1", "@graphql-codegen/schema-ast": "^4.1.0", "@graphql-typed-document-node/core": "^3.2.0", "@parcel/watcher": "^2.5.0", "@tanstack/router-devtools": "^1.81.2", "@tanstack/router-plugin": "^1.79.0", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "prettier": "3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.7.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "4.20.0"}, "trustedDependencies": ["@parcel/watcher", "@swc/core", "@tsparticles/engine", "esbuild"], "resolutions": {"strip-ansi": "^6.0.1", "string-width": "^4.2.3"}}