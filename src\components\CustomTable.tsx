import classNames from "classnames";
import { generatePageRange } from "@/lib/utils";
import { CustomTableProps } from "@/lib/typescript/types";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "./ui/pagination";

export const CustomTable: React.FC<CustomTableProps> = ({
  headers,
  children,
  paginationInfo,
  onPageChange,
  hasPagination,
}) => {
  const currentPage = paginationInfo?.page || 1;
  const totalPages = paginationInfo?.totalPages || 1;
  // const siblingCount = paginationInfo?.siblingCount;

  const pageRange = generatePageRange(paginationInfo);

  return (
    <div>
      <Table>
        {/* <TableCaption>A list of your recent invoices.</TableCaption> [&_tr:last-child]:border-0 */}
        <TableHeader className="hover:bg-primary">
          <TableRow className="hover:bg-primary">
            {headers.map((item, idx) => {
              const isLast = headers.length === idx + 1;
              const isFirst = idx === 0;

              const style = item?.style ? item?.style : "";

              return (
                <TableHead
                  key={idx}
                  className={classNames("hover:bg-primary", {
                    [style]: style,
                    "rounded-tr-lg": isLast,
                    "rounded-tl-lg": isFirst,
                  })}
                >
                  {item?.name}
                </TableHead>
              );
            })}
          </TableRow>
        </TableHeader>
        <TableBody>{children}</TableBody>
      </Table>

      {/* PAGINATION */}
      {hasPagination && (
        <div className="flex justify-between items-center mt-2">
          <p className="flex-shrink-0 font-normal">{`Page ${currentPage} of ${totalPages}`}</p>
          <div>
            <Pagination className="flex items-center justify-center space-x-2">
              <PaginationContent>
                {/* Previous Button */}
                <PaginationItem>
                  <PaginationPrevious
                    className="p-0"
                    href="#"
                    onClick={() =>
                      currentPage > 1 && onPageChange?.(currentPage - 1)
                    }
                    // disabled={currentPage === 1}
                  />
                </PaginationItem>

                {/* Dynamic Pagination Links */}
                {pageRange.map((page, index) =>
                  page === "dots" ? (
                    <PaginationItem key={`dots-${index}`}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  ) : (
                    <PaginationItem key={`page-${page}`}>
                      <PaginationLink
                        href="#"
                        isActive={currentPage === page}
                        onClick={() => onPageChange?.(page as number)}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ),
                )}

                {/* Next Button */}
                <PaginationItem>
                  <PaginationNext
                    className="p-0"
                    href="#"
                    onClick={() =>
                      currentPage < totalPages &&
                      onPageChange?.(currentPage + 1)
                    }
                    // disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};
