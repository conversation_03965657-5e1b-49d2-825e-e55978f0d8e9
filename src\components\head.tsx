import { Helmet } from "react-helmet-async";

type HeadProps = {
  title?: string;
  withoutSuffix?: boolean;
  faviconURL?: string;
};

export const Head = (props: HeadProps) => {
  // todo: flip logic and use 'withDefaultTitleSuffix' instead
  const { title, withoutSuffix, faviconURL } = props;
  const defaultTitle = "Heala Virtual Health Platform";
  const titleWithSuffix = title ? `${title} | ${defaultTitle}` : defaultTitle;
  const titleWithoutSuffix = title ? title : "";
  const titleToUse = withoutSuffix ? titleWithoutSuffix : titleWithSuffix;

  return (
    <>
      <Helmet>
        <title>{titleToUse}</title>
        {faviconURL && (
          <>
            <link rel="icon" href={faviconURL} />
            <link rel="apple-touch-icon" href={faviconURL} />
          </>
        )}
      </Helmet>
    </>
  );
};
