import { IconProps } from "@/lib/typescript/types";

export const ConsultationIcon: React.FC<IconProps> = ({ size, className }) => {
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_5641_57726)">
        <path d="M24 9.5C24.0024 8.87309 23.8363 8.25705 23.5192 7.71626C23.2021 7.17547 22.7455 6.72978 22.1972 6.42578C21.649 6.12178 21.0291 5.97061 20.4024 5.98809C19.7758 6.00557 19.1653 6.19105 18.6348 6.52515C18.1043 6.85924 17.6733 7.32968 17.3869 7.88731C17.1004 8.44494 16.9689 9.06928 17.0062 9.69508C17.0436 10.3209 17.2483 10.9252 17.599 11.4448C17.9497 11.9644 18.4336 12.3803 19 12.649V16C19 17.3261 18.4732 18.5979 17.5355 19.5355C16.5979 20.4732 15.3261 21 14 21C12.6739 21 11.4021 20.4732 10.4645 19.5355C9.52678 18.5979 9 17.3261 9 16V15.849C10.6928 15.501 12.2138 14.58 13.3067 13.2414C14.3997 11.9027 14.9977 10.2282 15 8.5V5.5C14.9984 4.0418 14.4184 2.64377 13.3873 1.61267C12.3562 0.581561 10.9582 0.00158817 9.5 0C9.10218 0 8.72064 0.158035 8.43934 0.43934C8.15804 0.720644 8 1.10218 8 1.5C8 1.89782 8.15804 2.27936 8.43934 2.56066C8.72064 2.84196 9.10218 3 9.5 3C9.82831 3 10.1534 3.06466 10.4567 3.1903C10.76 3.31594 11.0356 3.50009 11.2678 3.73223C11.4999 3.96438 11.6841 4.23998 11.8097 4.54329C11.9353 4.84661 12 5.1717 12 5.5V8.5C12 9.69347 11.5259 10.8381 10.682 11.682C9.83807 12.5259 8.69347 13 7.5 13C6.30653 13 5.16193 12.5259 4.31802 11.682C3.47411 10.8381 3 9.69347 3 8.5V5.5C3 4.83696 3.26339 4.20107 3.73223 3.73223C4.20107 3.26339 4.83696 3 5.5 3C5.89783 3 6.27936 2.84196 6.56066 2.56066C6.84197 2.27936 7 1.89782 7 1.5C7 1.10218 6.84197 0.720644 6.56066 0.43934C6.27936 0.158035 5.89783 0 5.5 0C4.0418 0.00158817 2.64377 0.581561 1.61267 1.61267C0.581561 2.64377 0.00158817 4.0418 0 5.5L0 8.5C0.00231804 10.2282 0.600342 11.9027 1.69328 13.2414C2.78622 14.58 4.30724 15.501 6 15.849V16C6 18.1217 6.84285 20.1566 8.34315 21.6569C9.84344 23.1571 11.8783 24 14 24C16.1217 24 18.1566 23.1571 19.6569 21.6569C21.1571 20.1566 22 18.1217 22 16V12.649C22.5964 12.3661 23.1006 11.9204 23.4545 11.3632C23.8084 10.806 23.9975 10.1601 24 9.5Z" />
      </g>
      <defs>
        <clipPath id="clip0_5641_57726">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
