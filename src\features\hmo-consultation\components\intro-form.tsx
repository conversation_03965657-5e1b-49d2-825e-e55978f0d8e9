import { useState } from "react";
import { useHMOStore } from "../hooks/use-hmo-store";
import { useFormContext } from "react-hook-form";
import { getNextStepFromIntroForm } from "../utils";
import { ConsultationOwner } from "./consultation-owner";
import { EnrolleeId } from "@/features/hmo-consultation/components/enrollee-id";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { ConsultationType } from "@/features/hmo-consultation/components/consultation-type";
import FormNextButton from "@/features/hmo-consultation/components/form-next-button";

export function IntroForm() {
  const providerFeatures = useHMOStore(s => s.providerFeatures);
  const isHospitalConsultation = useHMOStore(s => s.isHospitalConsultation)
  const hasFollowUpFeature = providerFeatures?.hasFollowUpFeature || false;

  const [foundUser, setFoundUser] = useState(false);

  const canSelectConsultationType = hasFollowUpFeature && foundUser && !isHospitalConsultation;

  const { watch, trigger } = useFormContext<FormSchemaType>();
  const isFollowUp = watch('isFollowUp')

  const nextStep = getNextStepFromIntroForm(!!isFollowUp, !!isHospitalConsultation)
  const consultationOwner = watch("consultationOwner");
  const hmoId = watch("hmoId")
  const showEnrolleeIdInput = consultationOwner && consultationOwner !== "";
  const showPrincipalInput =
    consultationOwner === "Dependant" &&
    hmoId &&
    hmoId !== "";

  const principalHmoId = watch("principalHmoId");
  const principalIdNotEmpty =
    principalHmoId !== "" && principalHmoId;

  const isConsultationOwnersValid = showPrincipalInput
    ? principalIdNotEmpty
    : true;

  async function validateStep() {
    if (showPrincipalInput) {
      if (canSelectConsultationType) {
        return trigger(['hmoId', 'principalHmoId', 'isFollowUp'])
      } else {
        return trigger(['hmoId', 'principalHmoId'])
      }
    } else {
      if (canSelectConsultationType) {
        return trigger(['hmoId', 'isFollowUp'])
      } else {
        return trigger(['hmoId'])
      }
    }
  }

  return (
    <form className="flex flex-col gap-4 mt-4">
      {/* Select Consultation Owner */}
      <ConsultationOwner />

      {/* HMO ID */}
      {showEnrolleeIdInput && <EnrolleeId setFoundUser={setFoundUser} name="hmoId" />}

      {/* Principal HMO ID */}
      {showPrincipalInput && <EnrolleeId setFoundUser={() => { }} name="principalHmoId" />}

      {foundUser && isConsultationOwnersValid && (
        <div>
          {canSelectConsultationType && <ConsultationType />}

          <FormNextButton
            nextStep={nextStep}
            onClick={validateStep}
            validate={validateStep}
          />
        </div>
      )}
    </form>
  )
}