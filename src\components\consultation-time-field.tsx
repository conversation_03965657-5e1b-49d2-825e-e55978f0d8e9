import { FormControl } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useDoctorAvailableTimesForDate } from "@/features/doctor-consultation/hooks/useDoctorAvailableTimes";
import { SelectProps } from "@radix-ui/react-select";
import { format } from "date-fns";
import * as React from "react";
import { useState } from "react";

type ConsultationTimeFieldProps = {
  selectedDate: Date | undefined;
  onChange: (value: string | undefined) => void;
  doctorProfileId: string | null | undefined;
} & SelectProps;

export function ConsultationTimeField(props: ConsultationTimeFieldProps) {
  const { selectedDate, doctorProfileId, onChange } = props;
  const [selectedTime, setSelectedTime] = useState<string | undefined>(
    undefined,
  );

  // reset selected time when new date is selected
  // I'm avoiding doing this with an effect.
  // See: https://react.dev/learn/you-might-not-need-an-effect#adjusting-some-state-when-a-prop-changes
  const [prevSelectedDate, setPrevSelectedDate] = useState<Date | null | undefined>(
    selectedDate
  );
  if (prevSelectedDate && selectedDate && format(prevSelectedDate, 'MM-dd-yyyy') !== format(selectedDate, 'MM-dd-yyyy')) {
    setSelectedTime("");
    setPrevSelectedDate(selectedDate);
  }

  const { availableTimes, noAvailableTimes, isLoading, error } =
    useDoctorAvailableTimesForDate(doctorProfileId, selectedDate);

  const renderAvailableTimes = React.useCallback(() => {
    return availableTimes.map((t) => (
      <SelectItem key={t._id} value={t.start}>
        {t.start} (GMT+1)
      </SelectItem>
    ));
  }, [availableTimes]);

  return (
    <Select
      value={selectedTime}
      onValueChange={(value) => {
        onChange(value)
        setSelectedTime(value)
      }}
    >
      <FormControl>
        <SelectTrigger loading={isLoading}>
          {!isLoading && error && (
            <span className="opacity-70 text-red-500">
              Error getting available time slots
            </span>
          )}
          {!isLoading && noAvailableTimes && (
            <span className="opacity-50">No Available times for date</span>
          )}
          {isLoading && <span className="opacity-50">Loading...</span>}
          {selectedTime ? <span>{selectedTime} (GMT+1)</span> : <span></span>}
        </SelectTrigger>
      </FormControl>
      <SelectContent>{renderAvailableTimes()}</SelectContent>
    </Select>
  );
}
