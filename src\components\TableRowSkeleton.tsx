import { Skeleton } from "./ui/skeleton";
import { TableCell, TableRow } from "./ui/table";

export const TableRowSkeleton = ({ cellCount }: { cellCount: number }) => {
  return (
    <TableRow>
      {Array(cellCount)
        .fill(cellCount)
        .map((_, idx) => {
          return (
            <TableCell key={idx}>
              <Skeleton className="h-10 w-full" />
            </TableCell>
          );
        })}
    </TableRow>
  );
};
