import React from "react";
import { apolloClient } from "@/lib/apollo-client";
import { ApolloProvider } from "@apollo/client";
import { queryConfig } from "@/lib/react-query";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from "react-helmet-async";

type AppProviderProps = {
  children: React.ReactNode;
};

export const AppProvider = ({ children }: AppProviderProps) => {
  const [queryClient] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: queryConfig,
      }),
  );

  return (
    <QueryClientProvider client={queryClient}>
      <HelmetProvider>
        <ApolloProvider client={apolloClient}>{children}</ApolloProvider>
      </HelmetProvider>
    </QueryClientProvider>
  );
};
