import { Chip } from "@mui/material";
// import { makeStyles } from "@mui/styles";

// const useStyles = makeStyles(() => ({
//   badge: {
//     "&.MuiChip-root": {
//       fontSize: "12px !important",
//       height: "2.7rem",
//       borderRadius: "1.3rem",
//       fontWeight: 500,
//     },
//   },
// }));

export const StatusPill = ({
  type,
  label,
  size = "medium",
  onHandleClick,
}: {
  type: "success" | "normal" | "error" | "warning";
  label: string;
  size?: "medium" | "small";
  onHandleClick?: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}) => {
  // const classes = useStyles();

  const bgColor =
    type === "success"
      ? "rgba(62, 165, 132, 0.1)"
      : type === "normal"
        ? "rgb(233,239,255)"
        : type === "error"
          ? "rgba(242, 24, 24, 0.1)"
          : type === "warning"
            ? "rgb(255,237,170, 0.3)"
            : "rgba(240, 240, 240, 1)";

  const textColor =
    type === "success"
      ? "#3EA584"
      : type === "normal"
        ? "#0046fa"
        : type === "error"
          ? "#f21818"
          : type === "warning"
            ? "#ffd333"
            : "#757886";

  return (
    <button
      style={{ border: "none", background: "transparent" }}
      onClick={(e) => {
        if (!onHandleClick) return;
        onHandleClick(e);
      }}
    >
      <Chip
        label={label}
        // className={classes.badge}
        size={size}
        sx={{
          cursor: onHandleClick ? "pointer" : "default",
          backgroundColor: bgColor,
          color: textColor,
          textTransform: "uppercase",
          fontWeight: 600,
          fontSize: "12px",
        }}
        clickable={Boolean(onHandleClick)}
      />
    </button>
  );
};
