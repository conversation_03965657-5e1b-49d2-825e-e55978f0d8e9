type CompanyType = {
  _id: string;
  name: string;
  providerId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

type PlanType = {
  _id: string;
  type: string;
  name: string;
  amount: number;
  description: string;
  allowedFeatures: {
    consultation: string;
  };
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type THMOUser = {
  _id: string;
  hmoId: string;
  providerId: string;
  companyId: CompanyType;
  createdAt: string;
  deactivated: boolean;
  email: string;
  expiryDate: string;
  firstName: string;
  lastName: string;
  noc: number;
  phone: string;
  plan: string;
  planId: PlanType;
  status: boolean;
  updatedAt: string;
  dob: string;
  gender: "Male" | "Female" | "male" | "female" | undefined;
  photo?: string;
};

export type GetHMOUserResponse = {
  data: Array<THMOUser>;
};

export type Consultation = {
  _id: string;
  status: string;
  createdThrough: string;
  patientSatisfied: boolean;
  isDelivery: boolean;
  isFollowUp: boolean;
  consultationOwner: string;
  symptoms: Symptom[];
  discomfortLevel:
    | "None"
    | "Mild"
    | "Moderate"
    | "Severe"
    | "Intense"
    | undefined;
  firstNotice: string;
  description: string;
  providerId: string;
  contactMedium: string;
  type: string;

  address: string;
  pharmacyCode: string;
  pharmacyName: string;
  pharmacyAddress: string;
  followUpConsultationId: string;
  patientData?: PatientData;
  doctorData?: Doctor;

  updatedAt: string;
};

export type Doctor = {
  _id: string;
  firstName: string;
  lastName: string;
  hospital?: string;
  gender: Gender | undefined;
  picture: string;
  rating: number;
  cadre: string;
  specialization?: string;
};

interface PatientData {
  _id: string;
  firstName: string;
  lastName: string;
  gender: Gender | undefined;
  phoneNumber: string;
  image: string;
  hmoId: string;
  email: string;
  dob: string;
  principalHmoId: string;
}

interface Symptom {
  _id: string;
  name: string;
}

export type Gender = "Male" | "Female" | "male" | "female";
