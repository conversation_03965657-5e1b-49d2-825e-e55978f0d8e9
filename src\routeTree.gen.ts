/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DrugPaymentTestImport } from './routes/drug-payment-test'
import { Route as BookingSuccessImport } from './routes/booking-success'
import { Route as AccessImport } from './routes/access'
import { Route as SubdomainImport } from './routes/$subdomain'
import { Route as HospitalIndexImport } from './routes/hospital/index'
import { Route as HospitalSubdomainImport } from './routes/hospital/$subdomain'
import { Route as HmoSubdomainImport } from './routes/hmo.$subdomain'
import { Route as DrugPaymentReferralIdImport } from './routes/drug-payment.$referralId'
import { Route as DrugPaymentSuccessOrderIdImport } from './routes/drug-payment-success.$orderId'
import { Route as DoctorProfileIdImport } from './routes/doctor.$profileId'
import { Route as DashboardDashboardLayoutImport } from './routes/dashboard/_dashboardLayout'
import { Route as HospitalSubdomainIndexImport } from './routes/hospital/$subdomain.index'
import { Route as HmoSubdomainIndexImport } from './routes/hmo.$subdomain.index'
import { Route as DashboardDashboardLayoutIndexImport } from './routes/dashboard/_dashboardLayout.index'
import { Route as OldHospitalHospitalSubdomainImport } from './routes/old-hospital/hospital.$subdomain'
import { Route as HospitalSubdomainStartImport } from './routes/hospital/$subdomain.start'
import { Route as HospitalSubdomainBookConsultationImport } from './routes/hospital/$subdomain.book-consultation'
import { Route as HmoSubdomainCreateConsultationImport } from './routes/hmo.$subdomain.create-consultation'
import { Route as DoctorProfileIdBookConsultationImport } from './routes/doctor_.$profileId.book-consultation'
import { Route as DashboardDashboardLayoutTestsImport } from './routes/dashboard/_dashboardLayout.tests'
import { Route as DashboardDashboardLayoutPrescriptionsImport } from './routes/dashboard/_dashboardLayout.prescriptions'
import { Route as DashboardDashboardLayoutConsultationsImport } from './routes/dashboard/_dashboardLayout.consultations'
import { Route as OldHospitalHospitalSubdomainIndexImport } from './routes/old-hospital/hospital.$subdomain.index'
import { Route as OldHospitalHospitalSubdomainStartImport } from './routes/old-hospital/hospital.$subdomain.start'
import { Route as OldHospitalHospitalSubdomainBookConsultationImport } from './routes/old-hospital/hospital.$subdomain.book-consultation'

// Create Virtual Routes

const DashboardImport = createFileRoute('/dashboard')()
const IndexLazyImport = createFileRoute('/')()

// Create/Update Routes

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const DrugPaymentTestRoute = DrugPaymentTestImport.update({
  id: '/drug-payment-test',
  path: '/drug-payment-test',
  getParentRoute: () => rootRoute,
} as any)

const BookingSuccessRoute = BookingSuccessImport.update({
  id: '/booking-success',
  path: '/booking-success',
  getParentRoute: () => rootRoute,
} as any)

const AccessRoute = AccessImport.update({
  id: '/access',
  path: '/access',
  getParentRoute: () => rootRoute,
} as any)

const SubdomainRoute = SubdomainImport.update({
  id: '/$subdomain',
  path: '/$subdomain',
  getParentRoute: () => rootRoute,
} as any)

const IndexLazyRoute = IndexLazyImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any).lazy(() => import('./routes/index.lazy').then((d) => d.Route))

const HospitalIndexRoute = HospitalIndexImport.update({
  id: '/hospital/',
  path: '/hospital/',
  getParentRoute: () => rootRoute,
} as any)

const HospitalSubdomainRoute = HospitalSubdomainImport.update({
  id: '/hospital/$subdomain',
  path: '/hospital/$subdomain',
  getParentRoute: () => rootRoute,
} as any)

const HmoSubdomainRoute = HmoSubdomainImport.update({
  id: '/hmo/$subdomain',
  path: '/hmo/$subdomain',
  getParentRoute: () => rootRoute,
} as any)

const DrugPaymentReferralIdRoute = DrugPaymentReferralIdImport.update({
  id: '/drug-payment/$referralId',
  path: '/drug-payment/$referralId',
  getParentRoute: () => rootRoute,
} as any)

const DrugPaymentSuccessOrderIdRoute = DrugPaymentSuccessOrderIdImport.update({
  id: '/drug-payment-success/$orderId',
  path: '/drug-payment-success/$orderId',
  getParentRoute: () => rootRoute,
} as any)

const DoctorProfileIdRoute = DoctorProfileIdImport.update({
  id: '/doctor/$profileId',
  path: '/doctor/$profileId',
  getParentRoute: () => rootRoute,
} as any)

const DashboardDashboardLayoutRoute = DashboardDashboardLayoutImport.update({
  id: '/_dashboardLayout',
  getParentRoute: () => DashboardRoute,
} as any)

const HospitalSubdomainIndexRoute = HospitalSubdomainIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => HospitalSubdomainRoute,
} as any)

const HmoSubdomainIndexRoute = HmoSubdomainIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => HmoSubdomainRoute,
} as any)

const DashboardDashboardLayoutIndexRoute =
  DashboardDashboardLayoutIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => DashboardDashboardLayoutRoute,
  } as any)

const OldHospitalHospitalSubdomainRoute =
  OldHospitalHospitalSubdomainImport.update({
    id: '/old-hospital/hospital/$subdomain',
    path: '/old-hospital/hospital/$subdomain',
    getParentRoute: () => rootRoute,
  } as any)

const HospitalSubdomainStartRoute = HospitalSubdomainStartImport.update({
  id: '/start',
  path: '/start',
  getParentRoute: () => HospitalSubdomainRoute,
} as any)

const HospitalSubdomainBookConsultationRoute =
  HospitalSubdomainBookConsultationImport.update({
    id: '/book-consultation',
    path: '/book-consultation',
    getParentRoute: () => HospitalSubdomainRoute,
  } as any)

const HmoSubdomainCreateConsultationRoute =
  HmoSubdomainCreateConsultationImport.update({
    id: '/create-consultation',
    path: '/create-consultation',
    getParentRoute: () => HmoSubdomainRoute,
  } as any)

const DoctorProfileIdBookConsultationRoute =
  DoctorProfileIdBookConsultationImport.update({
    id: '/doctor_/$profileId/book-consultation',
    path: '/doctor/$profileId/book-consultation',
    getParentRoute: () => rootRoute,
  } as any)

const DashboardDashboardLayoutTestsRoute =
  DashboardDashboardLayoutTestsImport.update({
    id: '/tests',
    path: '/tests',
    getParentRoute: () => DashboardDashboardLayoutRoute,
  } as any)

const DashboardDashboardLayoutPrescriptionsRoute =
  DashboardDashboardLayoutPrescriptionsImport.update({
    id: '/prescriptions',
    path: '/prescriptions',
    getParentRoute: () => DashboardDashboardLayoutRoute,
  } as any)

const DashboardDashboardLayoutConsultationsRoute =
  DashboardDashboardLayoutConsultationsImport.update({
    id: '/consultations',
    path: '/consultations',
    getParentRoute: () => DashboardDashboardLayoutRoute,
  } as any)

const OldHospitalHospitalSubdomainIndexRoute =
  OldHospitalHospitalSubdomainIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => OldHospitalHospitalSubdomainRoute,
  } as any)

const OldHospitalHospitalSubdomainStartRoute =
  OldHospitalHospitalSubdomainStartImport.update({
    id: '/start',
    path: '/start',
    getParentRoute: () => OldHospitalHospitalSubdomainRoute,
  } as any)

const OldHospitalHospitalSubdomainBookConsultationRoute =
  OldHospitalHospitalSubdomainBookConsultationImport.update({
    id: '/book-consultation',
    path: '/book-consultation',
    getParentRoute: () => OldHospitalHospitalSubdomainRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexLazyImport
      parentRoute: typeof rootRoute
    }
    '/$subdomain': {
      id: '/$subdomain'
      path: '/$subdomain'
      fullPath: '/$subdomain'
      preLoaderRoute: typeof SubdomainImport
      parentRoute: typeof rootRoute
    }
    '/access': {
      id: '/access'
      path: '/access'
      fullPath: '/access'
      preLoaderRoute: typeof AccessImport
      parentRoute: typeof rootRoute
    }
    '/booking-success': {
      id: '/booking-success'
      path: '/booking-success'
      fullPath: '/booking-success'
      preLoaderRoute: typeof BookingSuccessImport
      parentRoute: typeof rootRoute
    }
    '/drug-payment-test': {
      id: '/drug-payment-test'
      path: '/drug-payment-test'
      fullPath: '/drug-payment-test'
      preLoaderRoute: typeof DrugPaymentTestImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/_dashboardLayout': {
      id: '/dashboard/_dashboardLayout'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardDashboardLayoutImport
      parentRoute: typeof DashboardRoute
    }
    '/doctor/$profileId': {
      id: '/doctor/$profileId'
      path: '/doctor/$profileId'
      fullPath: '/doctor/$profileId'
      preLoaderRoute: typeof DoctorProfileIdImport
      parentRoute: typeof rootRoute
    }
    '/drug-payment-success/$orderId': {
      id: '/drug-payment-success/$orderId'
      path: '/drug-payment-success/$orderId'
      fullPath: '/drug-payment-success/$orderId'
      preLoaderRoute: typeof DrugPaymentSuccessOrderIdImport
      parentRoute: typeof rootRoute
    }
    '/drug-payment/$referralId': {
      id: '/drug-payment/$referralId'
      path: '/drug-payment/$referralId'
      fullPath: '/drug-payment/$referralId'
      preLoaderRoute: typeof DrugPaymentReferralIdImport
      parentRoute: typeof rootRoute
    }
    '/hmo/$subdomain': {
      id: '/hmo/$subdomain'
      path: '/hmo/$subdomain'
      fullPath: '/hmo/$subdomain'
      preLoaderRoute: typeof HmoSubdomainImport
      parentRoute: typeof rootRoute
    }
    '/hospital/$subdomain': {
      id: '/hospital/$subdomain'
      path: '/hospital/$subdomain'
      fullPath: '/hospital/$subdomain'
      preLoaderRoute: typeof HospitalSubdomainImport
      parentRoute: typeof rootRoute
    }
    '/hospital/': {
      id: '/hospital/'
      path: '/hospital'
      fullPath: '/hospital'
      preLoaderRoute: typeof HospitalIndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/_dashboardLayout/consultations': {
      id: '/dashboard/_dashboardLayout/consultations'
      path: '/consultations'
      fullPath: '/dashboard/consultations'
      preLoaderRoute: typeof DashboardDashboardLayoutConsultationsImport
      parentRoute: typeof DashboardDashboardLayoutImport
    }
    '/dashboard/_dashboardLayout/prescriptions': {
      id: '/dashboard/_dashboardLayout/prescriptions'
      path: '/prescriptions'
      fullPath: '/dashboard/prescriptions'
      preLoaderRoute: typeof DashboardDashboardLayoutPrescriptionsImport
      parentRoute: typeof DashboardDashboardLayoutImport
    }
    '/dashboard/_dashboardLayout/tests': {
      id: '/dashboard/_dashboardLayout/tests'
      path: '/tests'
      fullPath: '/dashboard/tests'
      preLoaderRoute: typeof DashboardDashboardLayoutTestsImport
      parentRoute: typeof DashboardDashboardLayoutImport
    }
    '/doctor_/$profileId/book-consultation': {
      id: '/doctor_/$profileId/book-consultation'
      path: '/doctor/$profileId/book-consultation'
      fullPath: '/doctor/$profileId/book-consultation'
      preLoaderRoute: typeof DoctorProfileIdBookConsultationImport
      parentRoute: typeof rootRoute
    }
    '/hmo/$subdomain/create-consultation': {
      id: '/hmo/$subdomain/create-consultation'
      path: '/create-consultation'
      fullPath: '/hmo/$subdomain/create-consultation'
      preLoaderRoute: typeof HmoSubdomainCreateConsultationImport
      parentRoute: typeof HmoSubdomainImport
    }
    '/hospital/$subdomain/book-consultation': {
      id: '/hospital/$subdomain/book-consultation'
      path: '/book-consultation'
      fullPath: '/hospital/$subdomain/book-consultation'
      preLoaderRoute: typeof HospitalSubdomainBookConsultationImport
      parentRoute: typeof HospitalSubdomainImport
    }
    '/hospital/$subdomain/start': {
      id: '/hospital/$subdomain/start'
      path: '/start'
      fullPath: '/hospital/$subdomain/start'
      preLoaderRoute: typeof HospitalSubdomainStartImport
      parentRoute: typeof HospitalSubdomainImport
    }
    '/old-hospital/hospital/$subdomain': {
      id: '/old-hospital/hospital/$subdomain'
      path: '/old-hospital/hospital/$subdomain'
      fullPath: '/old-hospital/hospital/$subdomain'
      preLoaderRoute: typeof OldHospitalHospitalSubdomainImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/_dashboardLayout/': {
      id: '/dashboard/_dashboardLayout/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardDashboardLayoutIndexImport
      parentRoute: typeof DashboardDashboardLayoutImport
    }
    '/hmo/$subdomain/': {
      id: '/hmo/$subdomain/'
      path: '/'
      fullPath: '/hmo/$subdomain/'
      preLoaderRoute: typeof HmoSubdomainIndexImport
      parentRoute: typeof HmoSubdomainImport
    }
    '/hospital/$subdomain/': {
      id: '/hospital/$subdomain/'
      path: '/'
      fullPath: '/hospital/$subdomain/'
      preLoaderRoute: typeof HospitalSubdomainIndexImport
      parentRoute: typeof HospitalSubdomainImport
    }
    '/old-hospital/hospital/$subdomain/book-consultation': {
      id: '/old-hospital/hospital/$subdomain/book-consultation'
      path: '/book-consultation'
      fullPath: '/old-hospital/hospital/$subdomain/book-consultation'
      preLoaderRoute: typeof OldHospitalHospitalSubdomainBookConsultationImport
      parentRoute: typeof OldHospitalHospitalSubdomainImport
    }
    '/old-hospital/hospital/$subdomain/start': {
      id: '/old-hospital/hospital/$subdomain/start'
      path: '/start'
      fullPath: '/old-hospital/hospital/$subdomain/start'
      preLoaderRoute: typeof OldHospitalHospitalSubdomainStartImport
      parentRoute: typeof OldHospitalHospitalSubdomainImport
    }
    '/old-hospital/hospital/$subdomain/': {
      id: '/old-hospital/hospital/$subdomain/'
      path: '/'
      fullPath: '/old-hospital/hospital/$subdomain/'
      preLoaderRoute: typeof OldHospitalHospitalSubdomainIndexImport
      parentRoute: typeof OldHospitalHospitalSubdomainImport
    }
  }
}

// Create and export the route tree

interface DashboardDashboardLayoutRouteChildren {
  DashboardDashboardLayoutConsultationsRoute: typeof DashboardDashboardLayoutConsultationsRoute
  DashboardDashboardLayoutPrescriptionsRoute: typeof DashboardDashboardLayoutPrescriptionsRoute
  DashboardDashboardLayoutTestsRoute: typeof DashboardDashboardLayoutTestsRoute
  DashboardDashboardLayoutIndexRoute: typeof DashboardDashboardLayoutIndexRoute
}

const DashboardDashboardLayoutRouteChildren: DashboardDashboardLayoutRouteChildren =
  {
    DashboardDashboardLayoutConsultationsRoute:
      DashboardDashboardLayoutConsultationsRoute,
    DashboardDashboardLayoutPrescriptionsRoute:
      DashboardDashboardLayoutPrescriptionsRoute,
    DashboardDashboardLayoutTestsRoute: DashboardDashboardLayoutTestsRoute,
    DashboardDashboardLayoutIndexRoute: DashboardDashboardLayoutIndexRoute,
  }

const DashboardDashboardLayoutRouteWithChildren =
  DashboardDashboardLayoutRoute._addFileChildren(
    DashboardDashboardLayoutRouteChildren,
  )

interface DashboardRouteChildren {
  DashboardDashboardLayoutRoute: typeof DashboardDashboardLayoutRouteWithChildren
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardDashboardLayoutRoute: DashboardDashboardLayoutRouteWithChildren,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

interface HmoSubdomainRouteChildren {
  HmoSubdomainCreateConsultationRoute: typeof HmoSubdomainCreateConsultationRoute
  HmoSubdomainIndexRoute: typeof HmoSubdomainIndexRoute
}

const HmoSubdomainRouteChildren: HmoSubdomainRouteChildren = {
  HmoSubdomainCreateConsultationRoute: HmoSubdomainCreateConsultationRoute,
  HmoSubdomainIndexRoute: HmoSubdomainIndexRoute,
}

const HmoSubdomainRouteWithChildren = HmoSubdomainRoute._addFileChildren(
  HmoSubdomainRouteChildren,
)

interface HospitalSubdomainRouteChildren {
  HospitalSubdomainBookConsultationRoute: typeof HospitalSubdomainBookConsultationRoute
  HospitalSubdomainStartRoute: typeof HospitalSubdomainStartRoute
  HospitalSubdomainIndexRoute: typeof HospitalSubdomainIndexRoute
}

const HospitalSubdomainRouteChildren: HospitalSubdomainRouteChildren = {
  HospitalSubdomainBookConsultationRoute:
    HospitalSubdomainBookConsultationRoute,
  HospitalSubdomainStartRoute: HospitalSubdomainStartRoute,
  HospitalSubdomainIndexRoute: HospitalSubdomainIndexRoute,
}

const HospitalSubdomainRouteWithChildren =
  HospitalSubdomainRoute._addFileChildren(HospitalSubdomainRouteChildren)

interface OldHospitalHospitalSubdomainRouteChildren {
  OldHospitalHospitalSubdomainBookConsultationRoute: typeof OldHospitalHospitalSubdomainBookConsultationRoute
  OldHospitalHospitalSubdomainStartRoute: typeof OldHospitalHospitalSubdomainStartRoute
  OldHospitalHospitalSubdomainIndexRoute: typeof OldHospitalHospitalSubdomainIndexRoute
}

const OldHospitalHospitalSubdomainRouteChildren: OldHospitalHospitalSubdomainRouteChildren =
  {
    OldHospitalHospitalSubdomainBookConsultationRoute:
      OldHospitalHospitalSubdomainBookConsultationRoute,
    OldHospitalHospitalSubdomainStartRoute:
      OldHospitalHospitalSubdomainStartRoute,
    OldHospitalHospitalSubdomainIndexRoute:
      OldHospitalHospitalSubdomainIndexRoute,
  }

const OldHospitalHospitalSubdomainRouteWithChildren =
  OldHospitalHospitalSubdomainRoute._addFileChildren(
    OldHospitalHospitalSubdomainRouteChildren,
  )

export interface FileRoutesByFullPath {
  '/': typeof IndexLazyRoute
  '/$subdomain': typeof SubdomainRoute
  '/access': typeof AccessRoute
  '/booking-success': typeof BookingSuccessRoute
  '/drug-payment-test': typeof DrugPaymentTestRoute
  '/dashboard': typeof DashboardDashboardLayoutRouteWithChildren
  '/doctor/$profileId': typeof DoctorProfileIdRoute
  '/drug-payment-success/$orderId': typeof DrugPaymentSuccessOrderIdRoute
  '/drug-payment/$referralId': typeof DrugPaymentReferralIdRoute
  '/hmo/$subdomain': typeof HmoSubdomainRouteWithChildren
  '/hospital/$subdomain': typeof HospitalSubdomainRouteWithChildren
  '/hospital': typeof HospitalIndexRoute
  '/dashboard/consultations': typeof DashboardDashboardLayoutConsultationsRoute
  '/dashboard/prescriptions': typeof DashboardDashboardLayoutPrescriptionsRoute
  '/dashboard/tests': typeof DashboardDashboardLayoutTestsRoute
  '/doctor/$profileId/book-consultation': typeof DoctorProfileIdBookConsultationRoute
  '/hmo/$subdomain/create-consultation': typeof HmoSubdomainCreateConsultationRoute
  '/hospital/$subdomain/book-consultation': typeof HospitalSubdomainBookConsultationRoute
  '/hospital/$subdomain/start': typeof HospitalSubdomainStartRoute
  '/old-hospital/hospital/$subdomain': typeof OldHospitalHospitalSubdomainRouteWithChildren
  '/dashboard/': typeof DashboardDashboardLayoutIndexRoute
  '/hmo/$subdomain/': typeof HmoSubdomainIndexRoute
  '/hospital/$subdomain/': typeof HospitalSubdomainIndexRoute
  '/old-hospital/hospital/$subdomain/book-consultation': typeof OldHospitalHospitalSubdomainBookConsultationRoute
  '/old-hospital/hospital/$subdomain/start': typeof OldHospitalHospitalSubdomainStartRoute
  '/old-hospital/hospital/$subdomain/': typeof OldHospitalHospitalSubdomainIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexLazyRoute
  '/$subdomain': typeof SubdomainRoute
  '/access': typeof AccessRoute
  '/booking-success': typeof BookingSuccessRoute
  '/drug-payment-test': typeof DrugPaymentTestRoute
  '/dashboard': typeof DashboardDashboardLayoutIndexRoute
  '/doctor/$profileId': typeof DoctorProfileIdRoute
  '/drug-payment-success/$orderId': typeof DrugPaymentSuccessOrderIdRoute
  '/drug-payment/$referralId': typeof DrugPaymentReferralIdRoute
  '/hospital': typeof HospitalIndexRoute
  '/dashboard/consultations': typeof DashboardDashboardLayoutConsultationsRoute
  '/dashboard/prescriptions': typeof DashboardDashboardLayoutPrescriptionsRoute
  '/dashboard/tests': typeof DashboardDashboardLayoutTestsRoute
  '/doctor/$profileId/book-consultation': typeof DoctorProfileIdBookConsultationRoute
  '/hmo/$subdomain/create-consultation': typeof HmoSubdomainCreateConsultationRoute
  '/hospital/$subdomain/book-consultation': typeof HospitalSubdomainBookConsultationRoute
  '/hospital/$subdomain/start': typeof HospitalSubdomainStartRoute
  '/hmo/$subdomain': typeof HmoSubdomainIndexRoute
  '/hospital/$subdomain': typeof HospitalSubdomainIndexRoute
  '/old-hospital/hospital/$subdomain/book-consultation': typeof OldHospitalHospitalSubdomainBookConsultationRoute
  '/old-hospital/hospital/$subdomain/start': typeof OldHospitalHospitalSubdomainStartRoute
  '/old-hospital/hospital/$subdomain': typeof OldHospitalHospitalSubdomainIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexLazyRoute
  '/$subdomain': typeof SubdomainRoute
  '/access': typeof AccessRoute
  '/booking-success': typeof BookingSuccessRoute
  '/drug-payment-test': typeof DrugPaymentTestRoute
  '/dashboard': typeof DashboardRouteWithChildren
  '/dashboard/_dashboardLayout': typeof DashboardDashboardLayoutRouteWithChildren
  '/doctor/$profileId': typeof DoctorProfileIdRoute
  '/drug-payment-success/$orderId': typeof DrugPaymentSuccessOrderIdRoute
  '/drug-payment/$referralId': typeof DrugPaymentReferralIdRoute
  '/hmo/$subdomain': typeof HmoSubdomainRouteWithChildren
  '/hospital/$subdomain': typeof HospitalSubdomainRouteWithChildren
  '/hospital/': typeof HospitalIndexRoute
  '/dashboard/_dashboardLayout/consultations': typeof DashboardDashboardLayoutConsultationsRoute
  '/dashboard/_dashboardLayout/prescriptions': typeof DashboardDashboardLayoutPrescriptionsRoute
  '/dashboard/_dashboardLayout/tests': typeof DashboardDashboardLayoutTestsRoute
  '/doctor_/$profileId/book-consultation': typeof DoctorProfileIdBookConsultationRoute
  '/hmo/$subdomain/create-consultation': typeof HmoSubdomainCreateConsultationRoute
  '/hospital/$subdomain/book-consultation': typeof HospitalSubdomainBookConsultationRoute
  '/hospital/$subdomain/start': typeof HospitalSubdomainStartRoute
  '/old-hospital/hospital/$subdomain': typeof OldHospitalHospitalSubdomainRouteWithChildren
  '/dashboard/_dashboardLayout/': typeof DashboardDashboardLayoutIndexRoute
  '/hmo/$subdomain/': typeof HmoSubdomainIndexRoute
  '/hospital/$subdomain/': typeof HospitalSubdomainIndexRoute
  '/old-hospital/hospital/$subdomain/book-consultation': typeof OldHospitalHospitalSubdomainBookConsultationRoute
  '/old-hospital/hospital/$subdomain/start': typeof OldHospitalHospitalSubdomainStartRoute
  '/old-hospital/hospital/$subdomain/': typeof OldHospitalHospitalSubdomainIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$subdomain'
    | '/access'
    | '/booking-success'
    | '/drug-payment-test'
    | '/dashboard'
    | '/doctor/$profileId'
    | '/drug-payment-success/$orderId'
    | '/drug-payment/$referralId'
    | '/hmo/$subdomain'
    | '/hospital/$subdomain'
    | '/hospital'
    | '/dashboard/consultations'
    | '/dashboard/prescriptions'
    | '/dashboard/tests'
    | '/doctor/$profileId/book-consultation'
    | '/hmo/$subdomain/create-consultation'
    | '/hospital/$subdomain/book-consultation'
    | '/hospital/$subdomain/start'
    | '/old-hospital/hospital/$subdomain'
    | '/dashboard/'
    | '/hmo/$subdomain/'
    | '/hospital/$subdomain/'
    | '/old-hospital/hospital/$subdomain/book-consultation'
    | '/old-hospital/hospital/$subdomain/start'
    | '/old-hospital/hospital/$subdomain/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$subdomain'
    | '/access'
    | '/booking-success'
    | '/drug-payment-test'
    | '/dashboard'
    | '/doctor/$profileId'
    | '/drug-payment-success/$orderId'
    | '/drug-payment/$referralId'
    | '/hospital'
    | '/dashboard/consultations'
    | '/dashboard/prescriptions'
    | '/dashboard/tests'
    | '/doctor/$profileId/book-consultation'
    | '/hmo/$subdomain/create-consultation'
    | '/hospital/$subdomain/book-consultation'
    | '/hospital/$subdomain/start'
    | '/hmo/$subdomain'
    | '/hospital/$subdomain'
    | '/old-hospital/hospital/$subdomain/book-consultation'
    | '/old-hospital/hospital/$subdomain/start'
    | '/old-hospital/hospital/$subdomain'
  id:
    | '__root__'
    | '/'
    | '/$subdomain'
    | '/access'
    | '/booking-success'
    | '/drug-payment-test'
    | '/dashboard'
    | '/dashboard/_dashboardLayout'
    | '/doctor/$profileId'
    | '/drug-payment-success/$orderId'
    | '/drug-payment/$referralId'
    | '/hmo/$subdomain'
    | '/hospital/$subdomain'
    | '/hospital/'
    | '/dashboard/_dashboardLayout/consultations'
    | '/dashboard/_dashboardLayout/prescriptions'
    | '/dashboard/_dashboardLayout/tests'
    | '/doctor_/$profileId/book-consultation'
    | '/hmo/$subdomain/create-consultation'
    | '/hospital/$subdomain/book-consultation'
    | '/hospital/$subdomain/start'
    | '/old-hospital/hospital/$subdomain'
    | '/dashboard/_dashboardLayout/'
    | '/hmo/$subdomain/'
    | '/hospital/$subdomain/'
    | '/old-hospital/hospital/$subdomain/book-consultation'
    | '/old-hospital/hospital/$subdomain/start'
    | '/old-hospital/hospital/$subdomain/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexLazyRoute: typeof IndexLazyRoute
  SubdomainRoute: typeof SubdomainRoute
  AccessRoute: typeof AccessRoute
  BookingSuccessRoute: typeof BookingSuccessRoute
  DrugPaymentTestRoute: typeof DrugPaymentTestRoute
  DashboardRoute: typeof DashboardRouteWithChildren
  DoctorProfileIdRoute: typeof DoctorProfileIdRoute
  DrugPaymentSuccessOrderIdRoute: typeof DrugPaymentSuccessOrderIdRoute
  DrugPaymentReferralIdRoute: typeof DrugPaymentReferralIdRoute
  HmoSubdomainRoute: typeof HmoSubdomainRouteWithChildren
  HospitalSubdomainRoute: typeof HospitalSubdomainRouteWithChildren
  HospitalIndexRoute: typeof HospitalIndexRoute
  DoctorProfileIdBookConsultationRoute: typeof DoctorProfileIdBookConsultationRoute
  OldHospitalHospitalSubdomainRoute: typeof OldHospitalHospitalSubdomainRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  IndexLazyRoute: IndexLazyRoute,
  SubdomainRoute: SubdomainRoute,
  AccessRoute: AccessRoute,
  BookingSuccessRoute: BookingSuccessRoute,
  DrugPaymentTestRoute: DrugPaymentTestRoute,
  DashboardRoute: DashboardRouteWithChildren,
  DoctorProfileIdRoute: DoctorProfileIdRoute,
  DrugPaymentSuccessOrderIdRoute: DrugPaymentSuccessOrderIdRoute,
  DrugPaymentReferralIdRoute: DrugPaymentReferralIdRoute,
  HmoSubdomainRoute: HmoSubdomainRouteWithChildren,
  HospitalSubdomainRoute: HospitalSubdomainRouteWithChildren,
  HospitalIndexRoute: HospitalIndexRoute,
  DoctorProfileIdBookConsultationRoute: DoctorProfileIdBookConsultationRoute,
  OldHospitalHospitalSubdomainRoute:
    OldHospitalHospitalSubdomainRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$subdomain",
        "/access",
        "/booking-success",
        "/drug-payment-test",
        "/dashboard",
        "/doctor/$profileId",
        "/drug-payment-success/$orderId",
        "/drug-payment/$referralId",
        "/hmo/$subdomain",
        "/hospital/$subdomain",
        "/hospital/",
        "/doctor_/$profileId/book-consultation",
        "/old-hospital/hospital/$subdomain"
      ]
    },
    "/": {
      "filePath": "index.lazy.tsx"
    },
    "/$subdomain": {
      "filePath": "$subdomain.tsx"
    },
    "/access": {
      "filePath": "access.tsx"
    },
    "/booking-success": {
      "filePath": "booking-success.tsx"
    },
    "/drug-payment-test": {
      "filePath": "drug-payment-test.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard",
      "children": [
        "/dashboard/_dashboardLayout"
      ]
    },
    "/dashboard/_dashboardLayout": {
      "filePath": "dashboard/_dashboardLayout.tsx",
      "parent": "/dashboard",
      "children": [
        "/dashboard/_dashboardLayout/consultations",
        "/dashboard/_dashboardLayout/prescriptions",
        "/dashboard/_dashboardLayout/tests",
        "/dashboard/_dashboardLayout/"
      ]
    },
    "/doctor/$profileId": {
      "filePath": "doctor.$profileId.tsx"
    },
    "/drug-payment-success/$orderId": {
      "filePath": "drug-payment-success.$orderId.tsx"
    },
    "/drug-payment/$referralId": {
      "filePath": "drug-payment.$referralId.tsx"
    },
    "/hmo/$subdomain": {
      "filePath": "hmo.$subdomain.tsx",
      "children": [
        "/hmo/$subdomain/create-consultation",
        "/hmo/$subdomain/"
      ]
    },
    "/hospital/$subdomain": {
      "filePath": "hospital/$subdomain.tsx",
      "children": [
        "/hospital/$subdomain/book-consultation",
        "/hospital/$subdomain/start",
        "/hospital/$subdomain/"
      ]
    },
    "/hospital/": {
      "filePath": "hospital/index.tsx"
    },
    "/dashboard/_dashboardLayout/consultations": {
      "filePath": "dashboard/_dashboardLayout.consultations.tsx",
      "parent": "/dashboard/_dashboardLayout"
    },
    "/dashboard/_dashboardLayout/prescriptions": {
      "filePath": "dashboard/_dashboardLayout.prescriptions.tsx",
      "parent": "/dashboard/_dashboardLayout"
    },
    "/dashboard/_dashboardLayout/tests": {
      "filePath": "dashboard/_dashboardLayout.tests.tsx",
      "parent": "/dashboard/_dashboardLayout"
    },
    "/doctor_/$profileId/book-consultation": {
      "filePath": "doctor_.$profileId.book-consultation.tsx"
    },
    "/hmo/$subdomain/create-consultation": {
      "filePath": "hmo.$subdomain.create-consultation.tsx",
      "parent": "/hmo/$subdomain"
    },
    "/hospital/$subdomain/book-consultation": {
      "filePath": "hospital/$subdomain.book-consultation.tsx",
      "parent": "/hospital/$subdomain"
    },
    "/hospital/$subdomain/start": {
      "filePath": "hospital/$subdomain.start.tsx",
      "parent": "/hospital/$subdomain"
    },
    "/old-hospital/hospital/$subdomain": {
      "filePath": "old-hospital/hospital.$subdomain.tsx",
      "children": [
        "/old-hospital/hospital/$subdomain/book-consultation",
        "/old-hospital/hospital/$subdomain/start",
        "/old-hospital/hospital/$subdomain/"
      ]
    },
    "/dashboard/_dashboardLayout/": {
      "filePath": "dashboard/_dashboardLayout.index.tsx",
      "parent": "/dashboard/_dashboardLayout"
    },
    "/hmo/$subdomain/": {
      "filePath": "hmo.$subdomain.index.tsx",
      "parent": "/hmo/$subdomain"
    },
    "/hospital/$subdomain/": {
      "filePath": "hospital/$subdomain.index.tsx",
      "parent": "/hospital/$subdomain"
    },
    "/old-hospital/hospital/$subdomain/book-consultation": {
      "filePath": "old-hospital/hospital.$subdomain.book-consultation.tsx",
      "parent": "/old-hospital/hospital/$subdomain"
    },
    "/old-hospital/hospital/$subdomain/start": {
      "filePath": "old-hospital/hospital.$subdomain.start.tsx",
      "parent": "/old-hospital/hospital/$subdomain"
    },
    "/old-hospital/hospital/$subdomain/": {
      "filePath": "old-hospital/hospital.$subdomain.index.tsx",
      "parent": "/old-hospital/hospital/$subdomain"
    }
  }
}
ROUTE_MANIFEST_END */
