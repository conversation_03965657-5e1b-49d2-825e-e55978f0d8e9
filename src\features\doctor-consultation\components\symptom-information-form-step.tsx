import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  SymptomInformationStepFormSchema,
  useDoctorConsultationFormDataStore,
} from "@/features/doctor-consultation/hooks/useDoctorConsultationFormData";
import { useDoctorConsultationFormSteps } from "@/features/doctor-consultation/hooks/useDoctorConsultationFormSteps";
import { SymptomSelectorInput } from "@/components/symptom-selector-input";

export function SymptomInformationStep() {
  const formDataStore = useDoctorConsultationFormDataStore();
  const { setCurrentStep } = useDoctorConsultationFormSteps();

  const form = useForm<z.infer<typeof SymptomInformationStepFormSchema>>({
    resolver: zodResolver(SymptomInformationStepFormSchema),
    defaultValues: {
      symptoms: formDataStore.symptoms,
      discomfortLevel: formDataStore.discomfortLevel,
      firstNotice: formDataStore.firstNotice,
      description: formDataStore.description,
    },
  });

  const onSubmit = (
    values: z.infer<typeof SymptomInformationStepFormSchema>,
  ) => {
    formDataStore.setFormData(values);
    setCurrentStep("booking-preferences");
  };

  return (
    <div>
      <h2 className="text-xl font-medium">Symptom Information</h2>
      <p className="text-[13px] text-tertiary mt-0.5 mb-2">
        Tell us what the problem is
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <FormField
            control={form.control}
            name="symptoms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>What are your symptoms?</FormLabel>
                <FormControl className="w-full">
                  <SymptomSelectorInput
                    onChange={field.onChange}
                    initialValues={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="discomfortLevel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>What is your discomfort level</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="None">None</SelectItem>
                    <SelectItem value="Mild">Mild</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Intense">Intense</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="firstNotice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  When did you start experiencing the symptoms?
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="This week">This week</SelectItem>
                    <SelectItem value="Last week">Last week</SelectItem>
                    <SelectItem value="One month ago">One month ago</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Anything else?</FormLabel>
                <FormControl>
                  <Textarea className="resize-none" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="pt-8 lg:pt-6">
            <Button className="w-full" type="submit">
              Next
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}