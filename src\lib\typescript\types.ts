export type IconProps = {
  size?: number | string;
  className: string;
};

export type NavigationItem = {
  name: string;
  icon: React.FC<IconProps>;
  path: string;
};

export type HeaderType = { name: string; style?: string }[];

export type ContactMedium = "Audio" | "Chat" | "Video";

export type ConsultationStatusType =
  | "Ongoing"
  | "Completed"
  | "Accepted"
  | "Pending";

export type ConsultationTableData = {
  dateTime: string;
  doctor: string;
  "consultation Medium": ContactMedium;
  Status: ConsultationStatusType;
};

export type ProfileDataType = {
  _id?: string | null | undefined;
  firstName?: string | null;
  lastName?: string | null;
  height?: number | null;
  weight?: number | null;
  bloodGroup?: string | null;
  genotype?: string | null | undefined;
  gender?: string | null | undefined;
  phoneNumber?: string | null | undefined;
  providerId?:
    | {
        __typename?: "ProviderType" | undefined;
        _id?: string | null | undefined;
      }
    | null
    | undefined;
  plan?: string | null | undefined;
  status?: string | null | undefined;
  consultations?: number | null | undefined;
  createdAt?: string;
  image?: string | null | undefined;
  rating?: number | null | undefined;
  pastIllness?:
    | {
        __typename?: "IdPastIllnessType" | undefined;
        id?:
          | {
              __typename?: "PastIllnessType" | undefined;
              _id?: string | null | undefined;
              name?: string | null | undefined;
            }
          | null
          | undefined;
      }[]
    | null
    | undefined;
  accountId?:
    | {
        __typename?: "Account" | undefined;
        _id?: string | null | undefined;
        email?: string | null | undefined;
      }
    | null
    | undefined;
};

export type ProfileType = {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  height: number;
  weight: number;
  bloodGroup: string;
  genotype: string;
  gender: string;
  phoneNumber: string;
  providerId: string;
  plan: unknown;
  status: unknown;
  consultations: number;
  image: string | null;
  rating: unknown;
  accountId: string;
};

export type ConsultationInfoCardType = {
  _id: string;
  doctorName: string;
  doctorImage: string | null;
  doctorImgFallback: string;
  doctorSpecialization: string;
  doctorRating: string;
  date: string;
  time: string;
  medium: string;
  fee: number;
  type: string;
  dateString: string;
};

export type PageInfoType =
  | {
      __typename?: "PageInfo";
      totalDocs?: number | null;
      limit?: number | null;
      offset?: number | null;
      hasPrevPage?: boolean | null;
      hasNextPage?: boolean | null;
      page?: number | null;
      totalPages?: number | null;
      pagingCounter?: number | null;
      prevPage?: number | null;
      nextPage?: number | null;
    }
  | null
  | undefined;

export type CustomTableProps =
  | {
      headers: { name: string; style?: string }[];
      children: React.ReactNode;
      hasPagination: true;
      paginationInfo: PageInfoType;
      onPageChange: (page: number) => void;
    }
  | {
      headers: { name: string; style?: string }[];
      children: React.ReactNode;
      hasPagination: false;
      paginationInfo?: never;
      onPageChange?: never;
    };

export type PrescriptionData = {
  __typename?: "Prescription";
  _id: string;
  consultation?: string | null;
  createdAt?: unknown | null;
  updatedAt?: unknown | null;
  doctor?: {
    __typename?: "DoctorProfile";
    _id?: string | null;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
  drugs?: Array<{
    __typename?: "PrescriptionDrug";
    drugName?: string | null;
    dosageQuantity?: string | null;
    dosageUnit?: string | null;
    route?: string | null;
    instructions?: string | null;
    dosageFrequency?: {
      __typename?: "DosageFreq";
      timing?: number | null;
      duration?: number | null;
    } | null;
  }> | null;
};

export type TestsRefData = {
  __typename?: "Referral";
  _id: string;
  type?: string | null;
  reason?: string | null;
  note?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  doctor?: {
    __typename?: "DoctorProfile";
    _id?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    picture?: string | null;
    specialization?: string | null;
    rating?: number | null;
  } | null;
  tests?: Array<{
    __typename?: "DiagnosticLabTest";
    _id?: string | null;
    name?: string | null;
    note?: string | null;
    urgency?: string | null;
    partner?: string | null;
    paid?: boolean | null;
    createdAt?: string | null;
    updatedAt?: string | null;
  }> | null;
};

export type ConsultationInfoCardBtnType = {
  time: string; // ISO date string
  consultationId: string;
};

export type TTimeLeft = {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
} | null;
