import { cn } from "@/lib/utils";
import { InfoIcon } from "./icons/info-icon";

export const Message = ({
  message,
  type,
}: {
  message: string;
  type: "info" | "error";
}) => {
  return (
    <div
      className={cn(
        "w-full flex items-center rounded-lg p-5 space-x-4",
        type === "info" ? "bg-[#E4F2FF]" : "bg-red-100",
      )}
    >
      <InfoIcon
        className={cn(type === "info" ? "fill-[#1A7ABF]" : "fill-red-500")}
      />
      <p
        className={cn(
          "text-sm font-light",
          type === "info" ? "text-[#1A7ABF]" : "text-red-500",
        )}
      >
        {message}
      </p>
    </div>
  );
};
