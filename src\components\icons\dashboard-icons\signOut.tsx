import { IconProps } from "@/lib/typescript/types";

export const SignoutIcon: React.FC<IconProps> = ({ size, className }) => {
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_5641_57744)">
        <path d="M11.5 16C11.1022 16 10.7206 16.158 10.4393 16.4393C10.158 16.7206 10 17.1022 10 17.5V18.3C10 19.0161 9.71554 19.7028 9.20919 20.2092C8.70284 20.7155 8.01608 21 7.3 21H5.7C4.98392 21 4.29716 20.7155 3.79081 20.2092C3.28446 19.7028 3 19.0161 3 18.3V5.7C3 4.98392 3.28446 4.29716 3.79081 3.79081C4.29716 3.28446 4.98392 3 5.7 3H7.3C8.01608 3 8.70284 3.28446 9.20919 3.79081C9.71554 4.29716 10 4.98392 10 5.7V6.5C10 6.89782 10.158 7.27936 10.4393 7.56066C10.7206 7.84196 11.1022 8 11.5 8C11.8978 8 12.2794 7.84196 12.5607 7.56066C12.842 7.27936 13 6.89782 13 6.5V5.7C12.9984 4.18875 12.3974 2.73986 11.3288 1.67125C10.2601 0.602632 8.81125 0.00158828 7.3 0L5.7 0C4.18875 0.00158828 2.73986 0.602632 1.67125 1.67125C0.602632 2.73986 0.00158828 4.18875 0 5.7L0 18.3C0.00158828 19.8112 0.602632 21.2601 1.67125 22.3288C2.73986 23.3974 4.18875 23.9984 5.7 24H7.3C8.81125 23.9984 10.2601 23.3974 11.3288 22.3288C12.3974 21.2601 12.9984 19.8112 13 18.3V17.5C13 17.1022 12.842 16.7206 12.5607 16.4393C12.2794 16.158 11.8978 16 11.5 16Z" />
        <path d="M22.561 9.52415L17.975 4.93815C17.8357 4.79888 17.6703 4.68842 17.4882 4.61308C17.3062 4.53773 17.1111 4.49898 16.9141 4.49902C16.7171 4.49907 16.5221 4.53792 16.3401 4.61335C16.1581 4.68878 15.9928 4.79932 15.8535 4.93865C15.5722 5.22004 15.4143 5.60165 15.4144 5.9995C15.4144 6.1965 15.4533 6.39156 15.5287 6.57355C15.6041 6.75554 15.7147 6.92088 15.854 7.06015L19.265 10.4712L7 10.4992C6.60218 10.4992 6.22064 10.6572 5.93934 10.9385C5.65804 11.2198 5.5 11.6013 5.5 11.9992C5.5 12.397 5.65804 12.7785 5.93934 13.0598C6.22064 13.3411 6.60218 13.4992 7 13.4992L19.318 13.4712L15.851 16.9382C15.5696 17.2194 15.4115 17.6009 15.4114 17.9988C15.4113 18.3967 15.5692 18.7783 15.8505 19.0597C16.1318 19.341 16.5133 19.4992 16.9111 19.4993C17.309 19.4994 17.6906 19.3414 17.972 19.0602L22.558 14.4742C23.2136 13.8175 23.5821 12.9277 23.5827 11.9998C23.5833 11.0719 23.2158 10.1816 22.561 9.52415Z" />
      </g>
      <defs>
        <clipPath id="clip0_5641_57744">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
