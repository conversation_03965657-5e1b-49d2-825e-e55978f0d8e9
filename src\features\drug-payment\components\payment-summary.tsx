interface PaymentSummaryProps {
  subtotal: number;
  deliveryPrice: number;
  total: number;
}

export function PaymentSummary({ subtotal, deliveryPrice, total }: PaymentSummaryProps) {
  return (
    <div className="space-y-3">
      <div className="flex justify-between">
        <span className="text-gray-600">Subtotal</span>
        <span className="font-medium">
          ₦{subtotal.toLocaleString('en-NG', { minimumFractionDigits: 2 })}
        </span>
      </div>
      
      <div className="flex justify-between">
        <span className="text-gray-600">Shipping</span>
        {deliveryPrice === 0 ? (
          <span className="text-emerald-600 font-medium">Free</span>
        ) : (
          <span className="font-medium">
            ₦{deliveryPrice.toLocaleString('en-NG', { minimumFractionDigits: 2 })}
          </span>
        )}
      </div>
      
      <div className="border-t border-gray-200 pt-3 mt-3">
        <div className="flex justify-between">
          <span className="font-medium text-gray-900">Total</span>
          <span className="font-bold text-gray-900">
            ₦{total.toLocaleString('en-NG', { minimumFractionDigits: 2 })}
          </span>
        </div>
      </div>
    </div>
  );
}
