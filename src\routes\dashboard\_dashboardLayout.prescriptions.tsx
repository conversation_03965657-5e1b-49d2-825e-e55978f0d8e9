import React, { useState } from "react";
import { useQuery } from "@apollo/client";
import { gql } from "@/graphql/generated";
import { format, isValid } from "date-fns";
import { defaultPageInfo } from "@/lib/mock-data";
import { NoData } from "@/components/EmptyStates";
import { StatusPill } from "@/components/StatusPill";
import { CustomTable } from "@/components/CustomTable";
import { createFileRoute } from "@tanstack/react-router";
import { TableCell, TableRow } from "@/components/ui/table";
import { TableRowSkeleton } from "@/components/TableRowSkeleton";
import { PageInfoType, PrescriptionData } from "@/lib/typescript/types";
import { formatName, getDailyDrugDuration, getDrugDuration } from "@/lib/utils";
import { selectDashboardData, useDashboardStore } from "@/store/dashboardStore";
import {
  prescriptionsTableHeader,
  prescriptionTableHeader,
} from "@/lib/table-headers";
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";

export const Route = createFileRoute(
  "/dashboard/_dashboardLayout/prescriptions",
)({
  component: Prescriptions,
});

const GET_ALL_PRESCRIPTIONS = gql(`
    query getAllPrescriptions($first: Int, $page: Int, $orderBy: String, $patientId: String) {
    getPrescriptions(
      filterBy: { patient: $patientId }
      page: $page
      first: $first
      orderBy: $orderBy
    ) {
      data {
        _id
        doctor{
          _id
          firstName
          lastName
        }
        consultation
        createdAt
        updatedAt
        drugs {
          drugName
          dosageQuantity
          dosageUnit
          route
          instructions
          dosageFrequency {
            timing
            duration
          }
        }
      }
      pageInfo {
        totalDocs
        limit
        offset
        hasPrevPage
        hasNextPage
        page
        totalPages
        pagingCounter
        prevPage
        nextPage
      }
    }
  }
`);

function Prescriptions() {
  const userProfile = useDashboardStore(selectDashboardData)?.profile;
  const patientId = userProfile?._id;

  const [openModal, setOpenModal] = useState(false);
  const [pageInfo, setPageInfo] = useState<PageInfoType>(defaultPageInfo);
  const [selectedPrescription, setSelectedPrescription] =
    useState<PrescriptionData | null>(null);

  const { loading, data, error } = useQuery(GET_ALL_PRESCRIPTIONS, {
    variables: {
      first: pageInfo?.limit,
      page: pageInfo?.page,
      orderBy: "-createdAt",
      patientId,
    },
    onCompleted(data) {
      setPageInfo(data.getPrescriptions.pageInfo);
    },
  });

  const prescriptions = data?.getPrescriptions.data || [];

  return (
    <main>
      <div>
        <div className="my-10">
          <CustomTable
            headers={prescriptionsTableHeader}
            hasPagination={true}
            paginationInfo={pageInfo}
            onPageChange={(page: number) =>
              setPageInfo((prev) => ({ ...prev, page }))
            }
          >
            {loading ? (
              Array(10)
                .fill(10)
                .map((_, idx) => {
                  return <TableRowSkeleton cellCount={4} key={idx} />;
                })
            ) : error ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData
                    isError={true}
                    text="An Error occurred while trying to fetch your Prescriptions"
                    info={error?.message}
                  />
                </TableCell>
              </TableRow>
            ) : prescriptions?.length < 1 ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData
                    isError={false}
                    text="No Prescriptions"
                    info={
                      <>
                        Prescriptions are automatically added <br /> when a
                        doctor prescribes one.
                      </>
                    }
                  />
                </TableCell>
              </TableRow>
            ) : (
              prescriptions.map((prescription, idx) => {
                const timeData = new Date(prescription?.createdAt);
                const date = isValid(new Date(timeData))
                  ? format(timeData, "PPP")
                  : "No date";
                const doctorName = formatName(
                  prescription?.doctor?.firstName,
                  prescription?.doctor?.lastName,
                  "Dr.",
                );

                return (
                  <React.Fragment key={idx}>
                    <TableRow key={idx}>
                      <TableCell className="whitespace-nowrap">
                        {date}
                      </TableCell>
                      <TableCell className="whitespace-nowrap">
                        {doctorName}
                      </TableCell>
                      <TableCell className="whitespace-nowrap">
                        {prescription?.drugs?.length}
                      </TableCell>
                      <TableCell className="flex flex-wrap whitespace-nowrap">
                        <StatusPill
                          label="View"
                          type="normal"
                          onHandleClick={() => {
                            setSelectedPrescription(prescription);
                            setOpenModal(true);
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                );
              })
            )}
          </CustomTable>
          <PrescriptionsModal
            open={openModal}
            setOpen={setOpenModal}
            prescriptionData={selectedPrescription || ({} as PrescriptionData)}
          />
        </div>
      </div>
    </main>
  );
}

export const PrescriptionsModal = ({
  open,
  setOpen,
  prescriptionData,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  prescriptionData: PrescriptionData;
}) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-[80%]">
        <DialogHeader>
          <DialogTitle>Prescription Details</DialogTitle>
        </DialogHeader>
        <div className="my-5 max-h-[70vh] overflow-y-auto">
          <CustomTable headers={prescriptionTableHeader} hasPagination={false}>
            {prescriptionData?.drugs?.map((drug, idx) => {
              return (
                <TableRow key={idx}>
                  <TableCell className="whitespace-nowrap">
                    {drug?.drugName || "No name"}
                  </TableCell>
                  <TableCell className="whitespace-nowrap">
                    {drug?.dosageUnit || "No dosage"}
                  </TableCell>
                  <TableCell className="whitespace-nowrap">
                    {getDrugDuration(Number(drug?.dosageFrequency?.timing))}{" "}
                    daily
                    <br /> For{" "}
                    {getDailyDrugDuration(
                      Number(drug?.dosageFrequency?.duration),
                    )}
                  </TableCell>
                  <TableCell className="">{drug?.route || "No mode"}</TableCell>
                  <TableCell className="">
                    {drug?.instructions || "No instructions"}
                  </TableCell>
                </TableRow>
              );
            })}
          </CustomTable>
        </div>
      </DialogContent>
    </Dialog>
  );
};
