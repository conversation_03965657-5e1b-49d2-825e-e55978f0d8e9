import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCreateConsultation } from "@/features/consultation/hooks/use-create-consultation";
import {
  PersonalInformationStepFormSchema,
  useDoctorConsultationFormDataStore,
} from "@/features/doctor-consultation/hooks/useDoctorConsultationFormData";
import { useToast } from "@/hooks/use-toast";
import { ConsultationDoctor } from "@/lib/factories";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

type PersonalInformationStepProps = {
  doctorProfile: ConsultationDoctor;
};

export function PersonalInformationStep(props: PersonalInformationStepProps) {
  const { doctorProfile } = props;
  const formDataStore = useDoctorConsultationFormDataStore();

  const form = useForm<z.infer<typeof PersonalInformationStepFormSchema>>({
    resolver: zodResolver(PersonalInformationStepFormSchema),
    defaultValues: {
      firstName: formDataStore.firstName,
      lastName: formDataStore.lastName,
      phoneNumber: formDataStore.phoneNumber,
      gender: formDataStore.gender,
    },
  });

  const { createConsultation } = useCreateConsultation();

  const { toast } = useToast();

  const onSubmit = async (
    values: z.infer<typeof PersonalInformationStepFormSchema>,
  ) => {
    if (
      !(
        formDataStore.email &&
        formDataStore.symptoms &&
        formDataStore.firstNotice &&
        formDataStore.discomfortLevel &&
        formDataStore.date &&
        formDataStore.time &&
        formDataStore.channel
      )
    ) {
      toast({
        title: "Please fill all the fields",
        description: "Please fill all the fields",
        variant: "destructive",
      });
      return;
    }

    createConsultation({
      // user info
      firstName: values.firstName,
      lastName: values.lastName,
      gender: values.gender,
      phoneNumber: values.phoneNumber,
      email: formDataStore.email,

      // previous steps data
      symptoms: formDataStore.symptoms,
      firstNotice: formDataStore.firstNotice,
      discomfortLevel: formDataStore.discomfortLevel,
      description: formDataStore.description,

      date: formDataStore.date,
      time: formDataStore.time,
      channel: formDataStore.channel,

      doctorId: doctorProfile.id,
      amount: doctorProfile.fee,
      providerId: doctorProfile?.providerId,
      consultationType: "scheduled",
      createdThrough: "doctor-direct",
    });
  };

  return (
    <div>
      <h2 className="text-xl font-medium">Personal Information</h2>
      <p className="text-[13px] text-tertiary mt-0.5 mb-3">
        Enter a few personal information for record
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <PhoneInput {...field} defaultCountry="NG" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gender</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-8 lg:pt-6">
            <Button
              className="w-full"
              type="submit"
              loading={form.formState.isSubmitting}
            >
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
