import * as React from "react";
import { cn } from "@/lib/utils";
import { useWidgetColor } from "@/hooks/useWidgetColor";

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    const widgetColor = useWidgetColor();
    return (
      <input
        type={type}
        style={{ outlineColor: widgetColor }}
        className={cn(
          "flex h-12 w-full rounded-md border border-input bg-white px-3 py-1 text-base lg:text-xs text-muted-foreground shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground placeholder:opacity-50 disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = "Input";

export { Input };
