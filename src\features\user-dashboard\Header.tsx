import { useBreadcrumbs } from "@/hooks/useBreadcrumbs";

export const Header = () => {
  const headerText = useBreadcrumbs();
  return (
    <header className="w-full h-full  flex justify-center items-center p-7">
      {/* DESKTOP VIEW */}
      <div className="hidden w-full md:flex items-center justify-between">
        <h1 className="whitespace-nowrap text-2xl font-medium text-black">
          {headerText}
        </h1>
        <div className="flex items-center space-x-4"></div>
      </div>

      {/* MOBILE VIEW */}
      <div className="w-full flex md:hidden items-center justify-between">
        <img src="" />
        <h1 className="whitespace-nowrap font-mont font-semibold text-xl text-black">
          {headerText}
        </h1>
      </div>
    </header>
  );
};
