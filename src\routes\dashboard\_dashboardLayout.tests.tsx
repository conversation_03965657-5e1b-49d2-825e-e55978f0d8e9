import { useState } from "react";
import { gql } from "@/graphql/generated";
import { useQuery } from "@apollo/client";
import { format, isValid } from "date-fns";
import { defaultPageInfo } from "@/lib/mock-data";
import { NoData } from "@/components/EmptyStates";
import { StatusPill } from "@/components/StatusPill";
import { formatName, getInitials } from "@/lib/utils";
import { CustomTable } from "@/components/CustomTable";
import { createFileRoute } from "@tanstack/react-router";
import { TableCell, TableRow } from "@/components/ui/table";
import { testReferralTableHeader } from "@/lib/table-headers";
import { TableRowSkeleton } from "@/components/TableRowSkeleton";
import { PageInfoType, TestsRefData } from "@/lib/typescript/types";
import { DoctorInfoCard } from "@/components/cards/ConsultationInfoCard";
import { selectDashboardData, useDashboardStore } from "@/store/dashboardStore";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/sheet";
import { Download } from "lucide-react";

export const Route = createFileRoute("/dashboard/_dashboardLayout/tests")({
  component: Tests,
});

const GET_TESTS_REFERRALS = gql(`
  query getAllTestReferrals($patientId: String, $first: Int, $page: Int, $orderBy: String) {
  getReferrals(
    filterBy: {
      patient: $patientId
    }
    orderBy: $orderBy
    page: $page
    first: $first
  ) {
    referral {
      _id
      doctor {
        _id
        firstName
        lastName
        picture
        specialization
        rating
      }
      tests {
        _id
        name
        note
        urgency
        partner
        paid
        createdAt
        updatedAt
      }
      type
      reason
      note
      createdAt
      updatedAt
    }
    pageInfo {
      totalDocs
      limit
      offset
      hasPrevPage
      hasNextPage
      page
      totalPages
      pagingCounter
      prevPage
      nextPage
    }
  }
}
  `);

function Tests() {
  const userProfile = useDashboardStore(selectDashboardData)?.profile;
  const patientId = userProfile?._id;

  const [openModal, setOpenModal] = useState(false);
  const [pageInfo, setPageInfo] = useState<PageInfoType>(defaultPageInfo);
  const [selectedTest, setSelectedTest] = useState<TestsRefData | null>(null);

  const { loading, data, error } = useQuery(GET_TESTS_REFERRALS, {
    variables: {
      first: pageInfo?.limit,
      page: pageInfo?.page,
      orderBy: "-createdAt",
      patientId,
    },
    onCompleted(data) {
      setPageInfo(data.getReferrals.pageInfo);
    },
  });

  const tests = data?.getReferrals?.referral || [];

  return (
    <main>
      <div>
        <div className="my-10">
          <CustomTable
            headers={testReferralTableHeader}
            hasPagination={true}
            paginationInfo={pageInfo}
            onPageChange={(page: number) =>
              setPageInfo((prev) => ({ ...prev, page }))
            }
          >
            {loading ? (
              Array(10)
                .fill(10)
                .map((_, idx) => {
                  return <TableRowSkeleton cellCount={4} key={idx} />;
                })
            ) : error ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData
                    isError={true}
                    text="An Error occurred while trying to fetch your Tests"
                    info={error?.message}
                  />
                </TableCell>
              </TableRow>
            ) : tests?.length < 1 ? (
              <TableRow className="hover:bg-white">
                <TableCell colSpan={4} className="text-center align-middle">
                  <NoData
                    isError={false}
                    text="No Tests"
                    info={
                      <>
                        Tests are automatically added when a <br /> doctor
                        recommends one.
                      </>
                    }
                  />
                </TableCell>
              </TableRow>
            ) : (
              tests.map((test, idx) => {
                const timeData = new Date(test?.createdAt);
                const date = isValid(new Date(timeData))
                  ? format(timeData, "PPP")
                  : "No date";
                const doctorName = formatName(
                  test?.doctor?.firstName,
                  test?.doctor?.lastName,
                  "Dr.",
                );

                return (
                  <TableRow key={idx}>
                    <TableCell className="whitespace-nowrap">{date}</TableCell>
                    <TableCell className="whitespace-nowrap">
                      {doctorName}
                    </TableCell>
                    <TableCell className="whitespace-nowrap">
                      {test?.tests?.length}
                    </TableCell>
                    <TableCell className="flex flex-wrap whitespace-nowrap">
                      <StatusPill
                        label="View"
                        type="normal"
                        onHandleClick={() => {
                          setSelectedTest(test);
                          setOpenModal(true);
                        }}
                      />
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </CustomTable>
          <TestsReferralsModal
            open={openModal}
            setOpen={setOpenModal}
            testRefData={selectedTest || ({} as TestsRefData)}
          />
        </div>
      </div>
    </main>
  );
}

const TestsReferralsModal = ({
  open,
  setOpen,
  testRefData,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  testRefData: TestsRefData;
}) => {
  const doctorName = formatName(
    testRefData?.doctor?.firstName || "",
    testRefData?.doctor?.lastName || "",
    "Dr.",
  );

  const timeData = new Date(`${testRefData?.createdAt}`);
  const date = isValid(new Date(timeData))
    ? format(timeData, "PPP")
    : "No date";

  const time = isValid(new Date(timeData)) ? format(timeData, "p") : "No Time";

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent className="p-0 lg:max-w-[600px]">
        <SheetHeader className="p-5">
          <SheetTitle>Test Summary</SheetTitle>
        </SheetHeader>
        <div className="h-full flex flex-col">
          <div className="mt-10 space-y-5 px-5">
            <DoctorInfoCard
              name={doctorName}
              image={testRefData?.doctor?.picture || ""}
              imgFallback={getInitials(doctorName)}
              specialization={testRefData?.doctor?.specialization || ""}
              rating={String(testRefData?.doctor?.rating)}
            />

            <div>
              <p className="font-medium">Date & Time Issued</p>
              <p className="text-sm text-slate-500">
                {date} at {time}
              </p>
            </div>
          </div>

          <div className="max-h-[70%] flex-1 bg-[#F1F5FC] px-5 py-10 mt-10 overflow-auto">
            <div className="space-y-10">
              {testRefData?.tests?.map((test, idx) => (
                <div key={idx} className="flex justify-between items-center">
                  <div>
                    <p className="text-xl font-medium">{test?.name}</p>
                    <p className="text-sm text-slate-500 space-x-2">
                      <span className="font-bold text-xs text-primary">
                        NOTE:
                      </span>
                      <span className="text-base">{test?.note || "Nil"}</span>
                    </p>
                  </div>
                  <button className="bg-white text-primary p-4 rounded-lg">
                    <Download />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
