import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { hospitalSteps, steps } from "@/features/hmo-consultation/utils";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { ArrowLeft } from "lucide-react";

type BackButtonProps = {
  step: keyof typeof steps | keyof typeof hospitalSteps;
};

export const BackButton = ({ step }: BackButtonProps) => {
  const widgetColor = useWidgetColor();
  const setFormStep = useHMOStore((s) => s.setFormStep);

  return (
    <div className="flex mt-3 mb-3">
      <p
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setFormStep(step);
        }}
        style={{ color: widgetColor }}
        className="flex items-center space-x-1 rounded-[4px] cursor-pointer"
      >
        <ArrowLeft
          style={{ color: widgetColor }}
          fontSize="inherit"
          className="w-4"
        />
        <span className="font-medium text-xs">Back</span>
      </p>
    </div>
  );
};
