import CalendarIcon from "@/components/icons/calendar-icon";
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import { isValidDate } from "@/lib/utils";
import { useFormContext } from "react-hook-form";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";

export function DOB() {
  const form = useFormContext<FormSchemaType>();

  const isDependent = form.getValues("consultationOwner") === "Dependant";

  return (
    <FormField
      control={form.control}
      name="dob"
      render={({ field }) => {
        return (
          <FormItem className="flex flex-col">
            <FormLabel className="mb-2 text-left">
              {isDependent ? "Dependant's Date of Birth" : "Date of Birth"}
            </FormLabel>

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                value={field.value ? new Date(field.value) : null}
                onChange={(val) =>
                  field.onChange(
                    val ? (isValidDate(val) ? val.toISOString() : null) : null,
                  )
                }
                slots={{
                  openPickerIcon: () => (
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  ),
                }}
                sx={{
                  ".MuiInputBase-root": {
                    background: "#fff",
                    height: 48,
                    "--tw-shadow": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
                    "--tw-shadow-colored": "0 1px 2px 0 var(--tw-shadow-color)",
                    "box-shadow":
                      "var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)",

                    borderRadius: "calc(var(--radius) - 2px)",
                    border: "1px solid hsl(var(--input))",

                    fontSize: 12,
                    color: "hsl(var(--muted-foreground))",

                    "&:focus-within": {
                      borderColor: "var(--primary)",
                      borderWidth: "2px",
                    },

                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                  },
                }}
              />
            </LocalizationProvider>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
