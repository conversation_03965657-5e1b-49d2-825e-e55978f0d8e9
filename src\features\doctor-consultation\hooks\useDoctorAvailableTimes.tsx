import { restApi } from "@/lib/rest-api-client";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { useMemo } from "react";

function convertServerTimeStringToCurrentTZTimeString(time: string) {
  // Split the time string into hours and minutes
  const [hours, minutes] = time.split(":").map(Number);

  // Create a Date object in UTC using the provided hours and minutes
  const utcDate = new Date(Date.UTC(1970, 0, 1, hours, minutes));

  // Get the local time string in the format HH:mm
  const localHours = String(utcDate.getHours()).padStart(2, "0");
  const localMinutes = String(utcDate.getMinutes()).padStart(2, "0");

  return `${localHours}:${localMinutes}`;
}

const getAvailableTimes = (times: Times[]) => {
  const withoutPassedSlots = times.filter((time) => {
    return time.passed !== true;
  });
  const onlyAvailableSlots = withoutPassedSlots?.filter((slot) => {
    return slot.available === true;
  });

  const withLocalTimeString = onlyAvailableSlots.map((s) => ({
    ...s,
    start: convertServerTimeStringToCurrentTZTimeString(s.start),
    end: convertServerTimeStringToCurrentTZTimeString(s.stop),
  }));

  return withLocalTimeString;
};

const getDoctorTimesForDate = async (
  date: Date,
  doctorId: string | null | undefined,
) => {
  if (!doctorId) {
    throw new Error(`Doctor Profile ID not defined. Value: ${doctorId}`);
  }

  const { data } = await restApi.get(
    `/doctors/availibility-for-date?date=${format(date, "yyyy-MM-dd")}&doctor=${doctorId}`
  );

  if (data.statusCode !== 200) {
    throw new Error(data.message);
  }

  return {
    times: data?.data?.times as Times[],
  };
};

export function useDoctorAvailableTimesForDate(
  doctorId: string | null | undefined,
  date: Date | undefined,
) {
  const { data, isLoading, error } = useQuery({
    queryFn: async () => getDoctorTimesForDate(date!, doctorId),
    queryKey: ["doctorTimesForDate", date && format(date, 'MM-dd-yyyy'), doctorId],
    enabled: !!date
  });

  if (error) {
    console.error(error);
  }

  const availableTimes = useMemo(() => {
    return getAvailableTimes(data?.times || []);
  }, [data?.times]);

  return {
    noAvailableTimes: !error && availableTimes.length === 0,
    availableTimes,
    isLoading,
    error,
  };
}

export type Times = {
  _id: string;
  start: string;
  stop: string;
  passed: boolean;
  available: boolean;
};
