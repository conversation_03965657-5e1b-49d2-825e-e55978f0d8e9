import { useQuery } from "@apollo/client";
import { gql } from "@/graphql/generated";

const GET_PENDING_SCHEDULED_CONSULTATIONS = gql(`
  query GetScheduledConsultations(
    $type: String
    $first: Int
    $orderBy: String
    $page: Int
    $acceptedStatus: String
    $pendingStatus: String
    $patientId: String
  ) {
    acceptedConsultations: getConsultations(
      filterBy: { type: $type, status: $acceptedStatus, patient: $patientId }
      first: $first
      orderBy: $orderBy
      page: $page
    ) {
      data {
        _id
        status
        type
        time
        fee
        contactMedium
        doctor {
          _id
          firstName
          lastName
          picture
          specialization
        }
        createdAt
      }
    }

    pendingConsultations: getConsultations(
      filterBy: { type: $type, status: $pendingStatus, patient: $patientId }
      first: $first
      orderBy: $orderBy
      page: $page
    ) {
      data {
        _id
        status
        type
        time
        fee
        contactMedium
        doctor {
          _id
          firstName
          lastName
          picture
          specialization
        }
        createdAt
      }
    }
  }
`);

export const useSortedConsultations = (variables: {
  type: string;
  pendingStatus: string;
  acceptedStatus: string;
  first: number;
  page: number;
  orderBy: string;
  patientId: string;
}) => {
  const { data, loading, error } = useQuery(
    GET_PENDING_SCHEDULED_CONSULTATIONS,
    {
      variables,
    },
  );

  // Extract consultations
  const acceptedConsultations = data?.acceptedConsultations?.data || [];
  const pendingConsultations = data?.pendingConsultations?.data || [];

  // Merge and sort by createdAt
  const sortedConsultations = [
    ...acceptedConsultations,
    ...pendingConsultations,
  ].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  return { sortedConsultations, loading, error };
};
