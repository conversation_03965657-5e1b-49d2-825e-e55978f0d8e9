import { useEffect, useState } from "react";
import { Message } from "./Message";
import { Button } from "./ui/button";
import { Loader2 } from "lucide-react";
import { gql } from "@/graphql/generated";
import { useMutation } from "@apollo/client";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "./ui/input-otp";

const resendOTPMutation = gql(`
    mutation resendOTP($email: String!) {
        resendOTP(data: { email: $email }) 
    }
`);

const verifyOTPMutation = gql(`
    mutation verifyEmail($email: String!, $otp: String!) {
        verifyEmail(data: {email: $email, otp: $otp}) {
            account {
                _id
                email
                dociId
                access_token
                refresh_token
                isEmailVerified
            }
        }
    }
`);

export const OTP = ({
  //   name,
  onComplete,
  email,
  //   type,
}: {
  name: string;
  onComplete: (info: { healaId: string; accessToken: string }) => void;
  email: string;
  type: "ACCESS";
}) => {
  const [otp, setOtp] = useState("");
  const [timeLeft, setTimeLeft] = useState(60);

  const [resendOTP, { loading: resendingOTP, error: resendOTPError }] =
    useMutation(resendOTPMutation, {
      onCompleted: () => {
        setTimeLeft(60);
      },
    });

  const [verifyOTP, { loading: verifyingOTP, error: verifyOTPError }] =
    useMutation(verifyOTPMutation, {
      onCompleted: (data) => {
        onComplete({
          healaId: data.verifyEmail.account.dociId || "",
          accessToken: data.verifyEmail.account.access_token || "",
        });
      },
    });

  const error = resendOTPError?.message || verifyOTPError?.message;
  const disabled = otp.length < 6 || verifyingOTP || resendingOTP;

  useEffect(() => {
    if (timeLeft === 0) return;
    const timerID = setInterval(() => {
      setTimeLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timerID);
  }, [timeLeft]);

  return (
    <div className="space-y-5">
      <div className="flex flex-col items-center space-y-2">
        {error && <Message message={error} type="error" />}
      </div>
      <InputOTP maxLength={6} value={otp} onChange={(e) => setOtp(e)}>
        <InputOTPGroup className="w-full flex justify-between">
          <InputOTPSlot index={0} />
          <InputOTPSlot index={1} />
          <InputOTPSlot index={2} />

          <InputOTPSlot index={3} />
          <InputOTPSlot index={4} />
          <InputOTPSlot index={5} />
        </InputOTPGroup>
      </InputOTP>

      <p className="flex flex-col lg:flex-row items-center justify-center space-x-2 text-center text-base text-[#99A6AB] lg:text-[18px] mt-5">
        <span className="text-base">Didn&apos;t get the code?</span>{" "}
        {timeLeft > 0 && (
          <span className="text-base font-medium">
            Resend Code ({timeLeft}s)
          </span>
        )}
        {timeLeft === 0 &&
          (resendingOTP ? (
            <Loader2 className="animate-spin text-primary ml-2" />
          ) : (
            <span
              onClick={() => {
                resendOTP({ variables: { email } });
              }}
              className="text-primary text-base font-medium underline cursor-pointer"
            >
              Resend Code
            </span>
          ))}
      </p>
      <div className="mt-5 space-y-2">
        <Button
          type="button"
          disabled={disabled}
          onClick={() => verifyOTP({ variables: { email, otp } })}
          loading={verifyingOTP}
          className="w-full"
        >
          Verify
        </Button>
      </div>
    </div>
  );
};
