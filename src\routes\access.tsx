import accessImg from "@/assets/assess-img.webp";
import { createFileRoute } from "@tanstack/react-router";
import healaWordMark from "@/assets/heala-workmark.svg";
import { LoginForm } from "@/features/access/LoginForm";
import { AccessOTPForm } from "@/features/access/accessOTPForm";
import { useAccessStore, selectAccessStep } from "@/store/accessStore";

export const Route = createFileRoute("/access")({
  component: RouteComponent,
});

const forms = {
  email: <LoginForm />,
  otp: <AccessOTPForm />,
};

function RouteComponent() {
  const accessStep = useAccessStore(selectAccessStep);
  return (
    <main className="h-screen grid grid-cols-1 lg:grid-cols-2">
      {/* IMAGE CONTAINER */}
      <div className="hidden lg:block bg-gray-300">
        <img
          src={accessImg}
          alt="Smiling lady in Hijab"
          className=" object-cover h-full w-full"
        />
      </div>
      {/* FORM CONTAINER */}
      <div className="bg-white flex flex-col">
        <div className="flex-1 flex items-center">
          {forms[accessStep as keyof typeof forms]}
        </div>
        <div className="my-3 mx-auto space-y-2">
          <div className="flex items-center space-x-3">
            <p>powered by</p>
            <img src={healaWordMark} alt="Heala word mark" />
          </div>
          <a
            className="hover:underline"
            target="_blank"
            href="https://heala.ng/terms/"
            rel="noreferrer"
          >
            <p className="text-sm text-primary">Heala's Terms & Conditions</p>
          </a>
        </div>
      </div>
    </main>
  );
}
