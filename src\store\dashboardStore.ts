import { create } from "zustand";
import { persist, StorageValue } from "zustand/middleware";
import { profileFactory } from "@/lib/factories/profile-factory";
import { ProfileDataType, ProfileType } from "@/lib/typescript/types";

type DashboardStoreType = {
  data: {
    healaId: string | null;
    profile: ProfileType;
  };
  setData: (data: Partial<DashboardStoreType["data"]>) => void;
};

export const useDashboardStore = create<DashboardStoreType>()(
  persist(
    (set) => ({
      data: {
        healaId: null,
        profile: profileFactory({} as ProfileDataType),
      },
      setData: (data: Partial<DashboardStoreType["data"]>) =>
        set((prev) => ({ data: { ...prev.data, ...data } })),
    }),
    {
      name: "dashboard-store",
      storage: {
        getItem: (key): StorageValue<DashboardStoreType> | null => {
          const item = sessionStorage.getItem(key);
          return item ? JSON.parse(item) : null;
        },
        setItem: (key, value) =>
          sessionStorage.setItem(key, JSON.stringify(value)),
        removeItem: (key) => sessionStorage.removeItem(key),
      },
    },
  ),
);

export const selectDashboardData = (state: DashboardStoreType) => state?.data;
export const selectSetDashboardData = (state: DashboardStoreType) =>
  state?.setData;
