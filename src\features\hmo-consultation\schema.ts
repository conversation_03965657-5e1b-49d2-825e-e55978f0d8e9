import * as yup from "yup";

export const principalHmoIdTestError = "IDs should not match";

export const symptomsSchema = yup.object().shape({
  name: yup.string().required(),
});

export const addressFeatureSchema = yup.object({
  address: yup.string().required("Please add an address"),
  isDelivery: yup.boolean().nullable(),
});

export const pharmacyFeatureSchema = yup
  .object({
    //address: yup.string().required("Please add an address"),
    pharmacyCode: yup.string().required(),
    pharmacyName: yup.string().when("pharmacyCode", {
      is: (value: string) => value && value !== "",
      then: (schema) => schema.required(),
      otherwise: (schema) => schema.notRequired(),
    }),
    pharmacyAddress: yup.string().when("pharmacyCode", {
      is: (value: string) => value && value !== "",
      then: (schema) => schema.required(),
      otherwise: (schema) => schema.notRequired(),
    }),
    // isDelivery: yup.boolean().nullable(),
  })
  .concat(addressFeatureSchema);

export const followUpSchema = yup.object({
  followUpConsultationId: yup.string().when("isFollowUp", {
    is: (value: boolean) => value && value === true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema.notRequired(),
  }),
  doctor: yup.string().when("isFollowUp", {
    is: (value: boolean) => value && value === true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const hospitalSchema = yup.object({
  time: yup.string().required("Select a time for the consultation"),
  type: yup.string().required("Type of consultation is required"),
  doctor: yup.string().required("Doctor is required"),
});

export const mainSchema = yup.object({
  symptoms: yup
    .array()
    .of(symptomsSchema)
    .min(1, "Select at least one symptom.")
    .required(),

  discomfortLevel: yup
    .string()
    .oneOf(
      ["None", "Mild", "Moderate", "Severe", "Intense"],
      "This field is required.",
    )
    .required("Select a discomfort level."),

  firstNotice: yup
    .string()
    // .oneOf(
    //   ["A few days ago", "Last Week", "One Month ago", "Last Year"],
    //   "This field is required."
    // )
    .required(),

  contactMedium: yup
    .string()
    .oneOf(["chat", "voice", "video"], "Select a contact medium.")
    .required("Select a contact medium."),

  gender: yup
    .string()
    .oneOf(["Male", "Female", "male", "female"], "Select a gender.")
    .required("Gender field is required."),

  description: yup.string(),
  image: yup.string().required(),
  hmoId: yup.string().required("HMO/Access ID is required."),
  lastName: yup.string().required("Last Name not found!"),
  firstName: yup.string().required("First Name not found!"),
  companyId: yup.string(),
  providerId: yup.string().required("Provider ID not found!"),
  phoneNumber: yup
    .string()
    .matches(/^(?:\+?234|0)?[789][01]\d{8}$/, "Invalid phone number")
    .required("Phone number is required"),
  email: yup
    .string()
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      "Invalid email format",
    )
    .required("Email is required"),

  dob: yup
    // .date()
    .string()
    .typeError("Select a Date to continue")
    .required(),

  consultationOwner: yup
    .mixed()
    .oneOf(["Myself", "Dependant", ""], "Select a consultation owner.")
    .required(),

  principalHmoId: yup.string().when("consultationOwner", {
    is: (value: string) => value === "Dependant",
    then: (schema) => schema.required("Principal's ID is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  isFollowUp: yup.bool().nullable(),
  createdThrough: yup.string().required("Created through is required"),
});

export const generateFullSchema = (hasPharmacyFeature: boolean) => {
  const pharmacySchema = hasPharmacyFeature
    ? mainSchema.concat(pharmacyFeatureSchema)
    : mainSchema;
  return (hasFollowUpFeature: boolean) => {
    const mergedFollowUpSchema = hasFollowUpFeature
      ? pharmacySchema.concat(followUpSchema)
      : pharmacySchema;
    return (isHospitalConsultation: boolean) => {
      const mergedHospitalSchema = isHospitalConsultation
        ? mergedFollowUpSchema.concat(hospitalSchema)
        : mergedFollowUpSchema;
      return (hasAddressFeature: boolean) => {
        const finalSchema = hasAddressFeature
          ? mergedHospitalSchema.concat(addressFeatureSchema)
          : mergedHospitalSchema;
        return finalSchema;
      };
    };
  };
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const fullSchema = mainSchema
  .concat(followUpSchema)
  .concat(pharmacyFeatureSchema);

export type FormSchemaType = yup.InferType<typeof fullSchema>;
