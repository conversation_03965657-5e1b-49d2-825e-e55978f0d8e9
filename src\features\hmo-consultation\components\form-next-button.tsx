import { But<PERSON> } from "@/components/ui/button"
import { FormStep } from "@/features/consultation/types"
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store"

type FormNextButtonProps = {
  nextStep: FormStep
  onClick?: () => void
  validate?: () => Promise<boolean>
}

export default function FormNextButton(props: FormNextButtonProps) {
  const { nextStep, onClick, validate } = props
  const { setFormStep } = useHMOStore()

  return (
    <Button
      onClick={async () => {
        try {
          const isValid = validate ? await validate() : true
          if (!isValid) throw new Error('Validation failed for step')

          onClick?.();

          if (!nextStep) return;
          setFormStep(nextStep);
        } catch (error) {
          console.log("Error from form button: ", error);
        }
      }}
      className="w-full mt-5"
      type="button"
    >Next</Button>
  )
}