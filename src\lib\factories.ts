import { DoctorPro<PERSON><PERSON> } from "@/graphql/generated/graphql";
import { z } from "zod";

const consultationDoctorSchema = z.object({
  id: z.string(),
  fee: z.number(),
  providerId: z.string(),
});

export type ConsultationDoctor = z.infer<typeof consultationDoctorSchema>;

export const makeConsultationDoctor = (
  doctor: DoctorProfile | null | undefined,
) => {
  const consultationDoctor = consultationDoctorSchema.safeParse({
    id: doctor?._id,
    fee: doctor?.fee,
    providerId: doctor?.providerId?._id,
  });

  if (consultationDoctor.error) {
    return null;
  } else {
    return consultationDoctor.data;
  }
};
