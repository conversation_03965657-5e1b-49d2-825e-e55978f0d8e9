import { SVGProps } from "react";

export const InfoIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_5987_59191)">
        <path d="M12 0C9.62663 0 7.30655 0.703788 5.33316 2.02236C3.35977 3.34094 1.8217 5.21509 0.913451 7.4078C0.00519943 9.60051 -0.232441 12.0133 0.230582 14.3411C0.693605 16.6689 1.83649 18.8071 3.51472 20.4853C5.19295 22.1635 7.33115 23.3064 9.65892 23.7694C11.9867 24.2324 14.3995 23.9948 16.5922 23.0866C18.7849 22.1783 20.6591 20.6402 21.9776 18.6668C23.2962 16.6935 24 14.3734 24 12C23.9966 8.81846 22.7312 5.76821 20.4815 3.51852C18.2318 1.26883 15.1815 0.00344108 12 0V0ZM12 21C10.22 21 8.47992 20.4722 6.99987 19.4832C5.51983 18.4943 4.36628 17.0887 3.68509 15.4442C3.0039 13.7996 2.82567 11.99 3.17294 10.2442C3.5202 8.49836 4.37737 6.89471 5.63604 5.63604C6.89472 4.37737 8.49836 3.5202 10.2442 3.17293C11.99 2.82567 13.7996 3.0039 15.4442 3.68508C17.0887 4.36627 18.4943 5.51983 19.4832 6.99987C20.4722 8.47991 21 10.22 21 12C20.9971 14.3861 20.0479 16.6736 18.3608 18.3607C16.6736 20.0479 14.3861 20.9971 12 21Z" />
        <path d="M11.5451 9.54528H11.2451C10.8521 9.53562 10.4696 9.67312 10.1727 9.93081C9.87578 10.1885 9.68583 10.5478 9.64007 10.9383C9.61391 11.3289 9.74151 11.7143 9.99562 12.0121C10.2497 12.3099 10.6102 12.4966 11.0001 12.5323V17.1823C11.0001 17.5801 11.1581 17.9616 11.4394 18.2429C11.7207 18.5242 12.1023 18.6823 12.5001 18.6823C12.8979 18.6823 13.2794 18.5242 13.5607 18.2429C13.8421 17.9616 14.0001 17.5801 14.0001 17.1823V12.0003C14.0001 11.3492 13.7414 10.7247 13.281 10.2643C12.8206 9.80393 12.1962 9.54528 11.5451 9.54528Z" />
        <path d="M11.8303 8.46636C12.1697 8.46636 12.5014 8.36572 12.7836 8.17717C13.0658 7.98861 13.2858 7.72061 13.4156 7.40705C13.5455 7.09349 13.5795 6.74846 13.5133 6.41559C13.4471 6.08272 13.2837 5.77696 13.0437 5.53697C12.8037 5.29698 12.4979 5.13355 12.165 5.06734C11.8322 5.00112 11.4871 5.03511 11.1736 5.16499C10.86 5.29487 10.592 5.51481 10.4035 5.79701C10.2149 6.0792 10.1143 6.41097 10.1143 6.75036C10.1141 6.97575 10.1584 7.19895 10.2446 7.4072C10.3308 7.61546 10.4572 7.80468 10.6166 7.96405C10.7759 8.12342 10.9652 8.24982 11.1734 8.33601C11.3817 8.4222 11.6049 8.4665 11.8303 8.46636Z" />
      </g>
      <defs>
        <clipPath id="clip0_5987_59191">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
