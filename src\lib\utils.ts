/* eslint-disable @typescript-eslint/no-explicit-any */
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { CountryCode, parsePhoneNumberFromString } from "libphonenumber-js";
import { ConsultationInfoCardType, PageInfoType } from "./typescript/types";
import { addMinutes, format, isPast, isValid } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const generatePageRange = (pageInfo: PageInfoType) => {
  const totalPages = pageInfo?.totalPages || 1;
  const currentPage = pageInfo?.page || 1;
  const siblingCount = Math.floor(((pageInfo?.limit || 1) - 1) / 2); // Example calculation for siblingCount
  const range: (number | "dots")[] = [];
  const totalPageNumbers = siblingCount * 2 + 5;

  if (totalPages <= totalPageNumbers) {
    for (let i = 1; i <= totalPages; i++) {
      range.push(i);
    }
  } else {
    const startPage = Math.max(2, currentPage - siblingCount);
    const endPage = Math.min(totalPages - 1, currentPage + siblingCount);

    if (startPage > 2) range.push(1, "dots");
    else range.push(1);

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    if (endPage < totalPages - 1) range.push("dots", totalPages);
    else range.push(totalPages);
  }

  return range;
};

// export const generatePageRange = (
//   siblingCount: number,
//   totalPages: number,
//   currentPage: number,
// ) => {
//   const range: (number | "dots")[] = [];
//   const totalPageNumbers = siblingCount * 2 + 5;

//   if (totalPages <= totalPageNumbers) {
//     for (let i = 1; i <= totalPages; i++) {
//       range.push(i);
//     }
//   } else {
//     const startPage = Math.max(2, currentPage - siblingCount);
//     const endPage = Math.min(totalPages - 1, currentPage + siblingCount);

//     if (startPage > 2) range.push(1, "dots");
//     else range.push(1);

//     for (let i = startPage; i <= endPage; i++) {
//       range.push(i);
//     }

//     if (endPage < totalPages - 1) range.push("dots", totalPages);
//     else range.push(totalPages);
//   }

//   return range;
// };

/**
 * Flattens an array of field errors into a single string message.
 *
 * Example:
 * Input: [
 *   { field: "email", message: "Invalid email format" },
 *   { field: "unknown", message: "Server error occurred" },
 *   { field: "age", message: "Must be over 18" }
 * ]
 * Output:
 *    "email: Invalid email format.
 *    Server error occurred. \n
 *    age: Must be over 18. \n"
 */
export function flattenFieldErrorsToSingleMessage(
  fieldErrors: Array<{ field: string; message: string }>,
) {
  return fieldErrors.reduce((acc, curr) => {
    const field = curr.field === "unknown" ? "" : curr.field + ": ";

    return acc + field + curr.message + ". \n";
  }, "");
}

export const isObject = (value: unknown) => {
  return (
    value !== null && (typeof value === "object" || typeof value === "function")
  );
};

export const formatName = (
  first_name: string | null | undefined,
  last_name: string | null | undefined,
  title?: string,
) => {
  const titleString = title ? title + " " : "";
  const firstNameString = first_name ? first_name + " " : "";
  const lastNameString = last_name ? last_name : "";
  return !first_name && !last_name
    ? "No name"
    : titleString + firstNameString + lastNameString;
};

export const getInitials = (name: string) => {
  try {
    const splittedNamesArr = name.split(" ");

    const initialsArr = splittedNamesArr.map((name) => {
      const splittedNameArr = name.split("");
      return splittedNameArr[0];
    });

    return initialsArr.join("")?.toUpperCase?.();
  } catch (error) {
    console.error("error from getInitials func.", error);
    return "";
  }
};

export const transformDataConsultationForConsultationInfoCard = (consultation: {
  __typename?: "Consultation";
  _id: string;
  status?: string | null;
  type?: string | null;
  time?: any | null;
  fee?: number | null;
  consultationOwner?: string | null;
  contactMedium?: string | null;
  createdAt?: any | null;
  updatedAt?: any | null;
  patient?: {
    __typename?: "Profile";
    _id?: string | null;
    firstName?: string | null;
  } | null;
  doctor?: {
    __typename?: "Doctor";
    _id?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    picture?: string | null;
    specialization?: string | null;
    rating?: number | null;
  } | null;
}) => {
  try {
    const doctorFirstName = consultation?.doctor?.firstName;
    const doctorLastName = consultation?.doctor?.lastName;
    const dateTimeData = new Date(consultation?.time);
    const formattedDate = isValid(dateTimeData)
      ? format(dateTimeData, "dd MMMM, yyyy")
      : "No Date";
    const formattedTime = isValid(dateTimeData)
      ? format(dateTimeData, "p")
      : "No Date";

    return {
      _id: consultation?._id,
      doctorName: formatName(
        doctorFirstName || "",
        doctorLastName || "",
        "Dr.",
      ),
      doctorImage: consultation?.doctor?.picture || "",
      doctorImgFallback: getInitials(
        formatName(doctorFirstName || "", doctorLastName || ""),
      ),
      doctorSpecialization:
        consultation?.doctor?.specialization || "No Specialization",
      doctorRating: consultation?.doctor?.rating || "No rating.",
      date: formattedDate,
      time: formattedTime,
      medium: consultation?.contactMedium || "No Medium",
      fee: consultation?.fee || 0,
      type: consultation?.type || "No Type",
      dateString: consultation?.time,
    } as ConsultationInfoCardType;
  } catch (error) {
    console.error(
      "Error from transformDataConsultationForConsultationInfoCard FN:",
      error,
    );
    return {} as ConsultationInfoCardType;
  }
};

export const getDrugDuration = (duration: number) => {
  let result = "";
  if (duration === 1) result = "once";
  if (duration === 2) result = "twice";
  if (duration === 3) result = "thrice";
  else if (duration > 3) result = `${duration} times`;
  return result;
};

export const getDailyDrugDuration = (value: number) => {
  let result = "";
  if (value === 1) result = `${value} day`;
  else result = `${value} days`;
  return result;
};

export const calculateRemainingTime = (targetTime: Date) => {
  const now = new Date();
  const difference = (targetTime?.getTime() || 0) - now.getTime();

  if (difference <= 0) return null;

  const days = Math.floor(difference / (1000 * 60 * 60 * 24));
  const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((difference / (1000 * 60)) % 60);
  const seconds = Math.floor((difference / 1000) % 60);

  return { days, hours, minutes, seconds };
};

export const isWaitTimeReached = (
  time: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } | null,
) => {
  if (!isObject(time)) return false;
  return (
    time.days <= 0 && time.hours <= 0 && time.minutes <= 0 && time?.seconds <= 0
  );
};

export const getFormattedTimeRemaining = (time: {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}) => {
  if (!isObject(time)) return null;
  return `${time.days > 0 ? `${time.days} Days` : ""} ${time.hours > 0 ? `${time.hours} Hrs` : ""} ${time.minutes}m${time.days < 1 ? `: ${time.seconds}s` : ""}`;
};

export const checkStatus = (time: string) => {
  try {
    const targetTime = new Date(time);
    const timePlus10mins = addMinutes(targetTime, 10);

    if (isPast(timePlus10mins)) return "EXPIRED";
    if (isPast(targetTime)) return "EXTRA_COUNTDOWN";

    return "COUNTING_DOWN";
  } catch (error) {
    console.error("Error from checkStatus FN:", error);
    return "EXPIRED";
  }
};

export function formatToNaira(amount: number) {
  return new Intl.NumberFormat("en-NG", {
    style: "currency",
    currency: "NGN",
    maximumFractionDigits: 0,
  }).format(amount);
}

export function setPrimaryColor(color: string) {
  if (typeof document !== "undefined" && document.documentElement) {
    document.documentElement.style.setProperty("--primary", color);
  } else {
    console.error("Unable to access the document to set the CSS variable.");
  }
}

type Manifest = {
  name?: string;
  short_name?: string;
  icons?: Array<{
    src: string;
    sizes: string;
    type: string;
  }>;

  // @ts-check no explicity any
  [key: string]: any; // Allow for other potential manifest properties
};

const updateManifest = async (
  url: string,
  manifest: Manifest,
): Promise<void> => {
  try {
    await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(manifest),
    });
  } catch (error) {
    console.error("Error injecting manifest:", error);
  }
};

export const formulateAndUpdateManifest = async (
  subDomain: string = "Heala App",
  widgetLogo: string = "https://heala-partners-2qzf8yrm7-heala-io.vercel.app/static/media/logo.723375eb.svg",
): Promise<void> => {
  try {
    // Update document title
    document.title = subDomain;

    // Update favicon
    const favicon = document.querySelector<HTMLLinkElement>("link[rel='icon']");
    if (favicon) {
      favicon.href = widgetLogo;
    }

    // Fetch and update manifest
    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    const response = await fetch(`${baseUrl}/manifest.json`);
    const oldManifest: Manifest = await response.json();

    const newManifest: Manifest = {
      ...oldManifest,
      name: subDomain,
      short_name: subDomain,
      icons: [
        {
          src: widgetLogo,
          sizes: "64x64 32x32 24x24 16x16",
          type: "image/x-icon",
        },
        {
          src: widgetLogo,
          type: "image/png",
          sizes: "192x192",
        },
      ],
    };

    await updateManifest(baseUrl, newManifest);
  } catch (error) {
    console.error("Error fetching manifest:", error);
  }
};

export function getProviderFeatures(providerId: string | undefined) {
  const pharmacyFeatureWhiteList = (
    import.meta.env.VITE_APP_PHARMACY_FEATURE_WHITELIST || ""
  ).split(" ");
  const followUpFeatureWhiteList = (
    import.meta.env.VITE_APP_FOLLOWUP_CONSULTATION_FEATURE_WHITELIST || ""
  ).split(" ");
  const addressFeatureWhiteList = (
    import.meta.env.VITE_APP_ADDRESS_FEATURE_WHITELIST || ""
  ).split(" ");

  if (!providerId) {
    return {
      hasPharmacyFeature: false,
      hasFollowUpFeature: false,
      hasAddressFeature: false,
    };
  }

  return {
    hasPharmacyFeature: pharmacyFeatureWhiteList.includes(providerId),
    hasFollowUpFeature: followUpFeatureWhiteList.includes(providerId),
    hasAddressFeature: addressFeatureWhiteList.includes(providerId),
  };
}

export const checkAddress = (addressObj: Record<string, string>) => {
  try {
    const index = Object.keys(addressObj || {}).findIndex(
      (key) => !addressObj[key] || addressObj[key] === "",
    );
    if (index === -1) return true;
    return false;
  } catch (error) {
    console.error("Error from checkAddress func.:", error);
    return false;
  }
};

export const defaultAddress = {
  // street: "",
  lga: "",
  state: "",
  city: "",
};

export const checkAndFormatAddress = (address: string) => {
  try {
    if (!address || address === "") return { ...defaultAddress };
    const addressArr = address.split(", ");
    const arrLength = addressArr?.length;
    //const street = addressArr.slice(0, arrLength - 2).join(" ");
    const lga = addressArr[arrLength - 2];
    const stateWithDot = addressArr[arrLength - 1];
    const stateArr = stateWithDot.split("");
    const state =
      stateArr[stateArr.length - 1] === "."
        ? stateArr.slice(0, stateArr.length - 1).join("")
        : stateArr.join("");

    return {
      /* street, */
      lga,
      state,
    };
  } catch (error) {
    console.error("Error from checkAndFormatAddress FN:", error);
    return { ...defaultAddress };
  }
};

export const removeDuplicateCommas = (inputString: string) => {
  // Use regular expression to replace multiple commas with a single comma
  const resultString = inputString.replace(/,+/g, ",");

  // If the string starts with a comma, remove it
  return resultString.startsWith(",") ? resultString.slice(1) : resultString;
};

export const trimObjectStrings = <T>(obj: T): T => {
  try {
    if (!isObject(obj)) return obj;

    for (const key in obj) {
      // @ts-expect-error - ...
      if (typeof obj[key] === "string") {
        // @ts-expect-error - ...
        obj[key] = obj[key].trim();
        // @ts-expect-error - ..
      } else if (typeof obj[key] === "object" && obj[key] !== null) {
        // @ts-expect-error - ..
        obj[key] = trimObjectStrings(obj[key]);
      }
    }
    return obj;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return obj;
  }
};

export const normalizeEmail = (email = "") => {
  try {
    if (!email) return email;
    const normalizedEmail = email.split("+").join("%2B");
    return normalizedEmail;
  } catch (error) {
    console.error(error);
    return email;
  }
};

export const isValidDate = (date: Date): boolean => !isNaN(date.getTime());

export function convertToInternationalFormat(
  phoneNumber: string,
  defaultCountry: CountryCode,
) {
  const parsedNumber = parsePhoneNumberFromString(phoneNumber, defaultCountry);

  if (parsedNumber && parsedNumber.isValid()) {
    return parsedNumber.formatInternational();
  } else {
    throw new Error("Invalid phone number");
  }
}

export const convertToSentenceCase = (string: string) => {
  return `${string[0]}`.toUpperCase() + `${string}`.slice(1).toLowerCase();
};

export const getAutoCompleteStyles = (
  borderColor: string,
  disabled: boolean,
) => ({
  "& .MuiInputBase-root": {
    height: "48px !important",
    color: "#334D57 !important",
    backgroundColor: disabled ? "#00212D0D" : "white",
    border: `1px solid rgba(128, 128, 128, 0.25)`,
    borderRadius: "5px",
    paddingRight: "10px !important",
    paddingLeft: "10px !important",
    display: "flex",
    alignItems: "center",
    "&.Mui-focused": {
      boxShadow: `0 0 0 1px ${borderColor}`,
    },
  },
  "& .MuiAutocomplete-endAdornment": {
    display: "flex",
  },
  "& .MuiOutlinedInput-root": {
    padding: "0px",
    "& input": {
      padding: "0px",
      border: "none",
      height: "30px",
      fontSize: { xs: "16px", sm: "16px", md: "16px", lg: "12px", xl: "12px" },
      "&:focus": {
        outline: "none", // Ensures no outline when focused
      },
    },
    "& button": {
      all: "unset",
      backgroundColor: "transparent", // MUI Autocomplete button background
      color: borderColor,
      cursor: "pointer",
    },
    "& :disabled": {
      color: "#334D57 !important",
    },
    "& fieldset": {
      border: "none", // Remove initial border
    },
    "&:hover fieldset": {
      borderColor: "#e2e8f0", // Change border color on hover
    },
    "&.Mui-focused fieldset": {
      border: "none", // Ensures no outline border when focused
    },
  },
});

export const isValueValidDate = (dateString: unknown) => {
  const types = ["string", "number", "Date", "object"];
  if (!types.includes(typeof dateString)) return false;

  const date = new Date(dateString as string | number | Date);
  return !isNaN(date?.getTime?.());
};

export const compose =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduceRight((acc, fn) => fn(acc), value);

export const convertExternalViaToCreatedThrough = (via: string) => {
  const createdThroughEnums = [
    "weblink",
    "doctor-direct",
    "app",
    "api",
    "video",
    "hospital-direct",
    "external-api",
  ];
  const externalCreatedThrough = `${via}`.toLowerCase();
  return createdThroughEnums.includes(externalCreatedThrough)
    ? externalCreatedThrough
    : "weblink";
};
