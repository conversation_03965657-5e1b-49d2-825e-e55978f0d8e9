import { createFileRoute } from "@tanstack/react-router";
import { DoctorInfoView } from "@/features/doctor-consultation/components/doctor-info-view";
import { useEffect } from "react";
import { API_KEY_LS_KEY } from "@/lib/constants";
import { env } from "@/config/env";

export const Route = createFileRoute("/doctor/$profileId")({
  component: RouteComponent,
});

function RouteComponent() {
  const { profileId } = Route.useParams();

  useEffect(() => {
    // we use the HEALA provider API key for REST requests in doctor weblink. Other
    // products (HMO, Hospital) use API key fetched per partner
    sessionStorage.setItem(API_KEY_LS_KEY, env.HEALA_PROVIDER_API_KEY);
  }, []);

  return <DoctorInfoView profileId={profileId} />;
}
