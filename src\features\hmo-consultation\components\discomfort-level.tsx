import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { useFormContext } from "react-hook-form"


export function DiscomfortLevel() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="discomfortLevel"
      render={({ field }) => (
        <FormItem>
          <FormLabel required>Discomfort level</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Enter when you first noticed your symptoms" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="None">None</SelectItem>
              <SelectItem value="Mild">Mild</SelectItem>
              <SelectItem value="Moderate">Moderate</SelectItem>
              <SelectItem value="Severe">Severe</SelectItem>
              <SelectItem value="Intense">Intense</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}