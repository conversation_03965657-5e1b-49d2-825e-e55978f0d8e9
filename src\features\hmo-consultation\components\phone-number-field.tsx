import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { PhoneInput } from "@/components/ui/phone-input"
import { FormSchemaType } from "@/features/hmo-consultation/schema"
import { useFormContext } from "react-hook-form"


export function PhoneNumber() {
  const form = useFormContext<FormSchemaType>()

  return (
    <FormField
      control={form.control}
      name="phoneNumber"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Phone Number</FormLabel>
          <FormControl>
            <PhoneInput {...field} defaultCountry="NG" />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}