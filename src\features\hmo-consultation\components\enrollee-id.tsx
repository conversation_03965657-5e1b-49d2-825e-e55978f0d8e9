import { <PERSON><PERSON><PERSON>oader } from "@/components/spinner-loader";
import {
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { GetEnrolleeIdModal } from "@/features/hmo-consultation/components/get-enrollee-id-modal";
import { useHMOStore } from "@/features/hmo-consultation/hooks/use-hmo-store";
import { FormSchemaType } from "@/features/hmo-consultation/schema";
import {
  GetHMOUserResponse,
  THMOUser,
} from "@/features/hmo-consultation/types";
import { addHMOUserDataToFormState } from "@/features/hmo-consultation/utils";
import { useWidgetColor } from "@/hooks/useWidgetColor";
import { restApi } from "@/lib/rest-api-client";
import { useQuery } from "@tanstack/react-query";
import { CircleCheckBig } from "lucide-react";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

const externalSearchWhitelist = (
  import.meta.env.VITE_APP_EXTERNAL_SEARCH_WHITELIST || ""
).split(" ");

const hpID = (import.meta.env.VITE_APP_HPID || "").split(" ");

type EnrolleeIdProps = {
  setFoundUser: React.Dispatch<React.SetStateAction<boolean>>;
  name: "hmoId" | "principalHmoId";
};

export function EnrolleeId(props: EnrolleeIdProps) {
  const appState = useHMOStore();
  const widgetColor = useWidgetColor();
  const { setFoundUser, name } = props;

  const { providerId } = appState?.partnerInfo || {};
  const { getValues, control, watch, setValue } =
    useFormContext<FormSchemaType>();

  const isDependentConsultation =
    `${getValues()?.consultationOwner}`.toLowerCase() === "dependant";

  const enrolleeType =
    isDependentConsultation && name === "hmoId"
      ? "dependant"
      : isDependentConsultation && name === "principalHmoId"
        ? "principal"
        : "enrollee";

  const isPrincipal = enrolleeType === "principal";
  const isDependant = enrolleeType === "dependant";
  const formHmoId = watch(name);

  const isExternalSearch = externalSearchWhitelist.includes(providerId);

  const {
    data: hmoUserById,
    status,
    isLoading: isHMOUserLoading,
    error: hmoUserError,
    // todo: Debounce...
  } = useQuery({
    queryKey: [name, formHmoId],
    queryFn: async () =>
      isExternalSearch
        ? await GET_HMO_USER_EXTERNAL(providerId!, formHmoId!)
        : await GET_HMO_USER(providerId!, formHmoId!),
    enabled: !!formHmoId,
    select: (response) => {
      if (isExternalSearch) {
        return response?.data?.data as unknown as THMOUser;
      } else {
        return response?.data?.data?.[0] as THMOUser;
      }
    },
  });

  const hmoUserData = hmoUserById;
  const foundUserForId = !!hmoUserData;

  useEffect(() => {
    if (isPrincipal) return;

    setFoundUser(foundUserForId);

    if (!hmoUserData) return;

    appState.setValues({
      userData: hmoUserData,
    });

    addHMOUserDataToFormState(setValue, hmoUserData);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hmoUserData, foundUserForId]);

  const label = isPrincipal
    ? "Principal HMO/Access ID"
    : isDependant
      ? "Dependant HMO/Access ID"
      : "HMO/Access ID";

  const placeHolder = isPrincipal
    ? "Enter Principal's ID"
    : isDependant
      ? "Enter dependant's ID"
      : "Enter your ID";

  const enrolleeInfo =
    "This is the HMO/Access ID given to you by your provider.";
  const principalInfo = "This is the ID of the Primary holder of the HMO plan.";
  const dependentInfo = "This is the ID of a beneficiary of the HMO plan.";

  const [showRetrieveIDModal, setShowRetrieveIDModal] = useState(false);

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel required className="">
            {label}
          </FormLabel>
          <FormDescription>
            {isPrincipal
              ? principalInfo
              : isDependant
                ? dependentInfo
                : enrolleeInfo}
          </FormDescription>

          <div className="flex gap-2 items-center">
            <Input {...field} placeholder={placeHolder} className="my-[2px]" />
            {isHMOUserLoading && <SpinnerLoader />}
            {foundUserForId &&
              !isHMOUserLoading /* && status !== "pending" */ && (
                <CircleCheckBig className="text-green-600" />
              )}
          </div>

          {!foundUserForId && !isHMOUserLoading && (
            <FormDescription>
              Can't remember your access id
              <span
                style={{ color: widgetColor }}
                className="cursor-pointer"
                onClick={() => setShowRetrieveIDModal(true)}
              >
                {" "}
                click here
              </span>
              {showRetrieveIDModal && (
                <GetEnrolleeIdModal
                  open={showRetrieveIDModal}
                  onClose={() => setShowRetrieveIDModal(false)}
                />
              )}
            </FormDescription>
          )}

          {!foundUserForId && hpID.includes(providerId) && (
            <FormDescription>
              Don't have an access id,
              <a
                href="https://onelink.to/wqu28p"
                target="_blank"
                className="cursor-pointer text-primary"
              >
                {" "}
                sign up.
              </a>
            </FormDescription>
          )}

          <FormMessage />

          {hmoUserError?.message && (
            <FormMessage>{hmoUserError?.message || null}</FormMessage>
          )}
          {!foundUserForId && !isHMOUserLoading && status !== "pending" && (
            <FormMessage>Invalid ID</FormMessage>
          )}
        </FormItem>
      )}
    />
  );
}

const GET_HMO_USER = async (providerId: string, hmoId: string) =>
  await restApi.get<GetHMOUserResponse>(
    `/enrollees?filterBy[providerId]=${encodeURIComponent(providerId)}&filterBy[hmoId]=${encodeURIComponent(hmoId.toUpperCase())}&page=1`,
  );

const GET_HMO_USER_EXTERNAL = async (providerId: string, hmoId: string) => {
  return await restApi.post<GetHMOUserResponse>(`/enrollees/search`, {
    providerId,
    hmoId: `${hmoId}`.toLowerCase(),
  });
};
